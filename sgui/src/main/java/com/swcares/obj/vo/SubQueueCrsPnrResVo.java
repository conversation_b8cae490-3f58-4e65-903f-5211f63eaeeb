package com.swcares.obj.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 关注PNR返回VO
 *
 * <AUTHOR>
 * @date 2025/08/04 17:45
 */
@Data
@ApiModel(value = "SubQueueCrsPnrResVo", description = "关注PNR返回VO")
public class SubQueueCrsPnrResVo implements Serializable {

    @ApiModelProperty(value = "是否成功")
    private Boolean success;

    @ApiModelProperty(value = "PNR详情DTO列表")
    private Object pnrDetailDtos;
}
