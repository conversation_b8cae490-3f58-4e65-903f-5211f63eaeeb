package com.swcares.obj.vo;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;

/**
 * 取消关注PNR返回VO
 *
 * <AUTHOR>
 * @date 2025/08/04 18:00
 */
@Data
@ApiModel(value = "UnsubscribeCrsQueueVo", description = "取消关注PNR返回VO")
public class UnsubscribeCrsQueueVo implements Serializable {

    // 根据response.json，data字段直接是boolean类型
    // 这里用Boolean包装类型表示操作结果
    private Boolean result;

    public UnsubscribeCrsQueueVo() {
    }

    public UnsubscribeCrsQueueVo(Boolean result) {
        this.result = result;
    }
}
