package com.swcares.service.et.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.swcares.core.exception.SguiResultException;
import com.swcares.core.security.custom.UserInfo;
import com.swcares.core.util.Constant;
import com.swcares.core.util.DateUtils;
import com.swcares.core.util.PinyinUtils;
import com.swcares.entity.*;
import com.swcares.obj.dto.BookPnrDto;
import com.swcares.obj.vo.BookPnrVo;
import com.swcares.service.*;
import com.swcares.service.et.IBookPnrService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 生成PNR服务实现类
 *
 * <AUTHOR>
 * @date 2025/5/19 14:00
 */
@Slf4j
@Service
public class BookPnrServiceImpl implements IBookPnrService {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private ISguiCommonService iSguiCommonService;

    @Resource
    private IMnjxPnrService iMnjxPnrService;

    @Resource
    private IMnjxPnrSegService iMnjxPnrSegService;

    @Resource
    private IMnjxPnrNmService iMnjxPnrNmService;

    @Resource
    private IMnjxPnrCtService iMnjxPnrCtService;

    @Resource
    private IMnjxPnrOsiService iMnjxPnrOsiService;

    @Resource
    private IMnjxPnrRmkService iMnjxPnrRmkService;

    @Resource
    private IMnjxPnrEiService iMnjxPnrEiService;

    @Resource
    private IMnjxPnrFcService iMnjxPnrFcService;

    @Resource
    private IMnjxPnrFpService iMnjxPnrFpService;

    @Resource
    private IMnjxPnrFnService iMnjxPnrFnService;

    @Resource
    private IMnjxPnrTkService iMnjxPnrTkService;

    @Resource
    private IMnjxPnrTcService iMnjxPnrTcService;

    @Resource
    private IMnjxNmTcService iMnjxNmTcService;

    @Resource
    private IMnjxPnrRecordService iMnjxPnrRecordService;

    @Resource
    private IMnjxPnrAtService iMnjxPnrAtService;

    @Resource
    private IMnjxNmCtService iMnjxNmCtService;

    @Resource
    private IMnjxNmEiService iMnjxNmEiService;

    @Resource
    private IMnjxNmFcService iMnjxNmFcService;

    @Resource
    private IMnjxNmFnService iMnjxNmFnService;

    @Resource
    private IMnjxNmFpService iMnjxNmFpService;

    @Resource
    private IMnjxNmOsiService iMnjxNmOsiService;

    @Resource
    private IMnjxNmRmkService iMnjxNmRmkService;

    @Resource
    private IMnjxNmSsrService iMnjxNmSsrService;

    @Resource
    private IMnjxNmXnService iMnjxNmXnService;

    @Resource
    private IMnjxOpenCabinService iMnjxOpenCabinService;

    @Resource
    private IMnjxAirportService iMnjxAirportService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BookPnrVo bookPnr(BookPnrDto dto) throws SguiResultException {
        // 参数校验
        if (dto == null) {
            throw new SguiResultException("请求参数不能为空");
        }
        if (CollUtil.isEmpty(dto.getFlights())) {
            throw new SguiResultException("航段信息不能为空");
        }
        if (CollUtil.isEmpty(dto.getPassengers())) {
            throw new SguiResultException("旅客信息不能为空");
        }

        // 创建PNR基本信息
        MnjxPnr pnr = this.createPnr();

        // 创建各类实体对象列表，用于批量保存
        List<MnjxPnrSeg> pnrSegList = new ArrayList<>();
        List<MnjxPnrNm> pnrNmList = new ArrayList<>();
        List<MnjxPnrCt> pnrCtList = new ArrayList<>();
        List<MnjxPnrOsi> pnrOsiList = new ArrayList<>();
        List<MnjxPnrRmk> pnrRmkList = new ArrayList<>();
        List<MnjxPnrTk> pnrTkList = new ArrayList<>();
        List<MnjxNmXn> nmXnList = new ArrayList<>();
        List<MnjxNmSsr> nmSsrList = new ArrayList<>();
        List<MnjxNmFp> nmFpList = new ArrayList<>();
        List<MnjxNmFn> nmFnList = new ArrayList<>();
        List<MnjxNmFc> nmFcList = new ArrayList<>();
        List<MnjxNmOsi> nmOsiList = new ArrayList<>();
        // PNR级别的价格信息列表
        List<MnjxPnrFp> pnrFpList = new ArrayList<>();
        List<MnjxPnrFn> pnrFnList = new ArrayList<>();
        List<MnjxPnrFc> pnrFcList = new ArrayList<>();
        // 封口记录列表
        List<MnjxPnrAt> pnrAtList = new ArrayList<>();
        // 其他可能需要的列表
        List<MnjxNmCt> nmCtList = new ArrayList<>();
        List<MnjxPnrTc> pnrTcList = new ArrayList<>();
        List<MnjxPnrEi> pnrEiList = new ArrayList<>();
        List<MnjxNmEi> nmEiList = new ArrayList<>();
        List<MnjxNmOi> nmOiList = new ArrayList<>();
        List<MnjxNmTc> nmTcList = new ArrayList<>();
        List<MnjxPnrNmTn> nmTnList = new ArrayList<>();
        List<MnjxNmRmk> nmRmkList = new ArrayList<>();

        long psgNum = dto.getPassengers().stream()
                .filter(p -> !"INF".equals(p.getPassengerType()))
                .count();

        // 处理航段信息
        List<MnjxOpenCabin> updateOpenCabinList = new ArrayList<>();
        this.processFlights(dto.getFlights(), pnr, pnrSegList, pnrAtList, psgNum, updateOpenCabinList);

        // 处理旅客信息
        Map<String, Integer> passengerIndexMap = this.processPassengers(dto.getPassengers(), pnr, pnrNmList, nmXnList, nmSsrList, nmOsiList, pnrSegList, nmRmkList);

        boolean fareWriteSuccess = true;
        // 处理价格信息
        if (!Boolean.TRUE.equals(dto.getNotNeedWritePrice()) && CollUtil.isNotEmpty(dto.getPrice())) {
            fareWriteSuccess = this.processPriceInfo(dto.getPrice(), pnr, pnrNmList, passengerIndexMap, nmFpList, nmFnList, nmFcList, pnrFpList, pnrFnList, pnrFcList, nmXnList, nmRmkList);
        }

        // 处理联系信息
        if (dto.getContactInfo() != null) {
            this.processContactInfo(dto.getContactInfo(), pnr, pnrCtList, pnrOsiList, pnrSegList.get(0).getFlightNo().substring(0, 2));
        }

        // 处理备注信息
        if (CollUtil.isNotEmpty(dto.getRemarks())) {
            this.processRemarks(dto.getRemarks(), pnr, pnrRmkList, nmRmkList, pnrOsiList, pnrSegList, pnrNmList, nmOsiList);
        }

        // 添加默认备注信息
        this.processDefaultRemarks(dto, pnr, pnrSegList, pnrRmkList, pnrEiList, nmEiList, pnrNmList, nmFcList, pnrFcList);

        // 处理特殊服务
        if (CollUtil.isNotEmpty(dto.getSpecialServices())) {
            this.processSpecialServices(dto.getSpecialServices(), pnrNmList, passengerIndexMap, nmSsrList, pnrSegList);
        }

        // 处理运价备注
        if (CollUtil.isNotEmpty(dto.getFareRemarks())) {
            // 运价备注组里如果输入了EI FP，则运价不能添加成功
            if (dto.getFareRemarks().stream().anyMatch(f -> StrUtil.equalsAny(f.getType(), "EI", "FP"))) {
                fareWriteSuccess = false;
            } else {
                this.processFareRemarks(dto.getFareRemarks(), dto.getPassengers(), pnr, pnrNmList, passengerIndexMap, pnrEiList, nmEiList, pnrTcList, nmTcList, pnrFpList, nmFpList);
            }
        }
        if (!fareWriteSuccess) {
            pnrFpList.clear();
            nmFpList.clear();
            pnrFcList.clear();
            nmFcList.clear();
            pnrFnList.clear();
            nmFnList.clear();
            pnrEiList.clear();
            nmEiList.clear();
        }

        // 处理出票时限
        if (dto.getTimeLimit() != null) {
            this.processTimeLimit(dto.getTimeLimit(), pnr, pnrTkList, dto.getFlights().get(0).getSegments().get(0));
        }

        // 在入库前重新排序所有项的pnr_index
        // 历史记录列表
        List<MnjxPnrRecord> pnrRecordList = new ArrayList<>();
        this.reorderPnrIndexes(pnr, pnrNmList, pnrSegList, pnrCtList, nmCtList, pnrTkList,
                pnrFcList, nmFcList, nmSsrList, pnrOsiList, nmOsiList, pnrRmkList, nmRmkList,
                pnrTcList, nmTcList, pnrFnList, nmFnList, pnrEiList, nmEiList, nmOiList,
                nmTnList, nmXnList, pnrFpList, nmFpList, pnrRecordList);

        // 更新PNR最大索引值
        // 注意：reorderPnrIndexes方法会更新pnr.maxIndex
        pnr.setCreateOfficeNoEid(pnr.getMaxIndex() + 1);

        // 批量保存所有数据
        this.batchSaveAllData(pnr, pnrNmList, pnrSegList, pnrCtList, nmCtList, pnrTkList,
                pnrFcList, nmFcList, nmSsrList, pnrOsiList, nmOsiList, pnrRmkList, nmRmkList,
                pnrTcList, nmTcList, pnrFnList, nmFnList, pnrEiList, nmEiList, pnrAtList, nmXnList, pnrFpList, nmFpList, pnrRecordList);
        iMnjxOpenCabinService.updateBatchById(updateOpenCabinList);

        // 构建返回结果
        BookPnrVo vo = new BookPnrVo();
        vo.setPnrNo(pnr.getPnrCrs());
        vo.setWritePriceSuccess(fareWriteSuccess);
        if (!fareWriteSuccess) {
            vo.setMessage("query_fare_error");
        }
        vo.setSegmentMarkSuccess(true);
        vo.setWriteCcvSuccess(true);

        // 如果之前进行了占位，需要取消占位
        // 获取当前用户名
        String username = iSguiCommonService.getCurrentUsername();
        if (StrUtil.isNotEmpty(username)) {
            // 从缓存中获取占位信息
            String cacheKey = "POR:" + username;
            String porJson = stringRedisTemplate.opsForValue().get(cacheKey);
            if (StrUtil.isNotEmpty(porJson)) {
                // 删除缓存即取消占位
                stringRedisTemplate.delete(cacheKey);
            }
        }

        return vo;
    }

    /**
     * Title: processFareRemarks
     * Description: 处理fareRemarks<br>
     *
     * @param fareRemarks
     * @param passengers
     * @param pnr
     * @param pnrNmList
     * @param passengerIndexMap
     * @param pnrEiList
     * @param nmEiList
     * @return void
     * <AUTHOR>
     * @date 2025/6/4 11:02
     */
    private void processFareRemarks(List<BookPnrDto.FareRemark> fareRemarks, List<BookPnrDto.Passenger> passengers, MnjxPnr pnr,
                                    List<MnjxPnrNm> pnrNmList, Map<String, Integer> passengerIndexMap,
                                    List<MnjxPnrEi> pnrEiList, List<MnjxNmEi> nmEiList, List<MnjxPnrTc> pnrTcList, List<MnjxNmTc> nmTcList,
                                    List<MnjxPnrFp> pnrFpList, List<MnjxNmFp> nmFpList) throws SguiResultException {
        for (BookPnrDto.FareRemark fareRemark : fareRemarks) {
            List<String> passengerIds = fareRemark.getPassengerIds();
            if (CollUtil.isNotEmpty(passengerIds)) {
                for (String passengerId : passengerIds) {
                    if (!passengerIndexMap.containsKey(passengerId)) {
                        // 没找到时可能是婴儿
                        List<BookPnrDto.Passenger> collect = passengers.stream()
                                .filter(p -> passengerId.equals(p.getInfantRef()))
                                .collect(Collectors.toList());
                        if (CollUtil.isNotEmpty(collect)) {
                            String id = collect.get(0).getId();
                            if (passengerIndexMap.containsKey(id)) {
                                Integer passengerPnrIndex = passengerIndexMap.get(passengerId);
                                MnjxPnrNm pnrNm = null;

                                // 从列表中查找旅客
                                for (MnjxPnrNm nm : pnrNmList) {
                                    if (nm.getPnrIndex().equals(passengerPnrIndex)) {
                                        pnrNm = nm;
                                        break;
                                    }
                                }

                                if (pnrNm == null) {
                                    continue;
                                }
                                if ("EI".equals(fareRemark.getType())) {
                                    // 构建婴儿的EI
                                    MnjxNmEi infantEi = new MnjxNmEi();
                                    infantEi.setNmEiId(IdUtil.getSnowflake(1, 1).nextIdStr());
                                    infantEi.setPnrNmId(pnrNm.getPnrNmId());
                                    infantEi.setPnrIndex(0); // 先设置为0，后续会重新排序

                                    String infantEiInfo = "EI/" + fareRemark.getText() + "/P" + passengerIndexMap.get(id);
                                    infantEi.setEiInfo(infantEiInfo);
                                    infantEi.setInputValue(infantEiInfo);
                                    nmEiList.add(infantEi);
                                } else if ("TC".equals(fareRemark.getType())) {
                                    // 构建婴儿的TC
                                    MnjxNmTc infantNmTc = new MnjxNmTc();
                                    infantNmTc.setNmTcId(IdUtil.getSnowflake(1, 1).nextIdStr());
                                    infantNmTc.setPnrNmId(pnrNm.getPnrNmId());
                                    infantNmTc.setPnrIndex(0); // 先设置为0，后续会重新排序
                                    infantNmTc.setTcInfo(fareRemark.getText().replace("F/", "TC/") + "/P" + passengerIndexMap.get(id));
                                    infantNmTc.setInputValue(fareRemark.getText().replace("F/", "TC/") + "/P" + passengerIndexMap.get(id));

                                    nmTcList.add(infantNmTc);
                                } else if ("FP".equals(fareRemark.getType())) {
                                    if (!StrUtil.equalsAny(fareRemark.getText(), "IN/CASH,CNY", "CASH,CNY")) {
                                        throw new SguiResultException("CODE");
                                    }
                                    // 构建婴儿的FP
                                    MnjxNmFp infantNmFp = new MnjxNmFp();
                                    infantNmFp.setNmFpId(IdUtil.getSnowflake(1, 1).nextIdStr());
                                    infantNmFp.setPnrNmId(pnrNm.getPnrNmId());
                                    infantNmFp.setPnrIndex(0); // 先设置为0，后续会重新排序
                                    infantNmFp.setIsBaby(fareRemark.getText().contains("IN/") ? 1 : 0);
                                    infantNmFp.setInputValue("FP/" + fareRemark.getText() + "/P" + passengerIndexMap.get(id));

                                    nmFpList.add(infantNmFp);
                                }
                            }
                        }
                        continue;
                    }

                    Integer passengerPnrIndex = passengerIndexMap.get(passengerId);
                    MnjxPnrNm pnrNm = null;

                    // 从列表中查找旅客
                    for (MnjxPnrNm nm : pnrNmList) {
                        if (nm.getPnrIndex().equals(passengerPnrIndex)) {
                            pnrNm = nm;
                            break;
                        }
                    }

                    if (pnrNm == null) {
                        continue;
                    }

                    if ("EI".equals(fareRemark.getType())) {
                        // 创建运价备注信息
                        MnjxNmEi nmEi = new MnjxNmEi();
                        nmEi.setNmEiId(IdUtil.getSnowflake(1, 1).nextIdStr());
                        nmEi.setPnrNmId(pnrNm.getPnrNmId());
                        nmEi.setPnrIndex(0); // 先设置为0，后续会重新排序
                        String inputValue = "EI/" + fareRemark.getText() + "/P" + pnrNm.getPsgIndex();
                        nmEi.setEiInfo(inputValue);
                        nmEi.setInputValue(inputValue);

                        nmEiList.add(nmEi);
                    } else if ("TC".equals(fareRemark.getType())) {
                        MnjxNmTc nmTc = new MnjxNmTc();
                        nmTc.setNmTcId(IdUtil.getSnowflake(1, 1).nextIdStr());
                        nmTc.setPnrNmId(pnrNm.getPnrNmId());
                        nmTc.setPnrIndex(0); // 先设置为0，后续会重新排序
                        nmTc.setTcInfo(fareRemark.getText().replace("F/", "TC/") + "/P" + pnrNm.getPsgIndex());
                        nmTc.setInputValue(fareRemark.getText().replace("F/", "TC/") + "/P" + pnrNm.getPsgIndex());

                        nmTcList.add(nmTc);
                    } else if ("FP".equals(fareRemark.getType())) {
                        if (!StrUtil.equalsAny(fareRemark.getText(), "IN/CASH,CNY", "CASH,CNY")) {
                            throw new SguiResultException("CODE");
                        }
                        MnjxNmFp nmFp = new MnjxNmFp();
                        nmFp.setNmFpId(IdUtil.getSnowflake(1, 1).nextIdStr());
                        nmFp.setPnrNmId(pnrNm.getPnrNmId());
                        nmFp.setPnrIndex(0); // 先设置为0，后续会重新排序
                        nmFp.setIsBaby(fareRemark.getText().contains("IN/") ? 1 : 0);
                        nmFp.setInputValue("FP/" + fareRemark.getText() + "/P" + pnrNm.getPsgIndex());

                        nmFpList.add(nmFp);
                    }
                }
            } else {
                if ("EI".equals(fareRemark.getType())) {
                    MnjxPnrEi pnrEi = new MnjxPnrEi();
                    pnrEi.setPnrEiId(IdUtil.getSnowflake(1, 1).nextIdStr());
                    pnrEi.setPnrId(pnr.getPnrId());
                    pnrEi.setPnrIndex(0); // 先设置为0，后续会重新排序
                    pnrEi.setEiInfo("EI/" + fareRemark.getText());
                    pnrEi.setInputValue("EI/" + fareRemark.getText());

                    pnrEiList.add(pnrEi);
                } else if ("TC".equals(fareRemark.getType())) {
                    MnjxPnrTc pnrTc = new MnjxPnrTc();
                    pnrTc.setPnrTcId(IdUtil.getSnowflake(1, 1).nextIdStr());
                    pnrTc.setPnrId(pnr.getPnrId());
                    pnrTc.setPnrIndex(0); // 先设置为0，后续会重新排序
                    pnrTc.setTcInfo(fareRemark.getText().replace("F/", "TC/"));
                    pnrTc.setInputValue(fareRemark.getText().replace("F/", "TC/"));

                    pnrTcList.add(pnrTc);
                } else if ("FP".equals(fareRemark.getType())) {
                    if (!StrUtil.equalsAny(fareRemark.getText(), "IN/CASH,CNY", "CASH,CNY")) {
                        throw new SguiResultException("CODE");
                    }
                    MnjxPnrFp pnrFp = new MnjxPnrFp();
                    pnrFp.setPnrFpId(IdUtil.getSnowflake(1, 1).nextIdStr());
                    pnrFp.setPnrId(pnr.getPnrId());
                    pnrFp.setPnrIndex(0); // 先设置为0，后续会重新排序
                    pnrFp.setIsBaby(fareRemark.getText().contains("IN/") ? 1 : 0);
                    pnrFp.setInputValue("FP/" + fareRemark.getText());

                    pnrFpList.add(pnrFp);
                }
            }
        }
    }

    /**
     * 批量保存所有数据
     */
    private void batchSaveAllData(MnjxPnr pnr, List<MnjxPnrNm> pnrNmList, List<MnjxPnrSeg> pnrSegList,
                                  List<MnjxPnrCt> pnrCtList, List<MnjxNmCt> nmCtList, List<MnjxPnrTk> pnrTkList,
                                  List<MnjxPnrFc> pnrFcList, List<MnjxNmFc> nmFcList, List<MnjxNmSsr> nmSsrList,
                                  List<MnjxPnrOsi> pnrOsiList, List<MnjxNmOsi> nmOsiList, List<MnjxPnrRmk> pnrRmkList,
                                  List<MnjxNmRmk> nmRmkList, List<MnjxPnrTc> pnrTcList, List<MnjxNmTc> nmTcList, List<MnjxPnrFn> pnrFnList,
                                  List<MnjxNmFn> nmFnList, List<MnjxPnrEi> pnrEiList, List<MnjxNmEi> nmEiList,
                                  List<MnjxPnrAt> pnrAtList, List<MnjxNmXn> nmXnList,
                                  List<MnjxPnrFp> pnrFpList, List<MnjxNmFp> nmFpList, List<MnjxPnrRecord> pnrRecordList) {
        // 保存PNR基本信息
        iMnjxPnrService.save(pnr);

        // 批量保存各类数据
        if (CollUtil.isNotEmpty(pnrSegList)) {
            iMnjxPnrSegService.saveBatch(pnrSegList);
        }
        if (CollUtil.isNotEmpty(pnrNmList)) {
            iMnjxPnrNmService.saveBatch(pnrNmList);
        }
        if (CollUtil.isNotEmpty(pnrCtList)) {
            iMnjxPnrCtService.saveBatch(pnrCtList);
        }
        if (CollUtil.isNotEmpty(nmCtList)) {
            iMnjxNmCtService.saveBatch(nmCtList);
        }
        if (CollUtil.isNotEmpty(pnrOsiList)) {
            iMnjxPnrOsiService.saveBatch(pnrOsiList);
        }
        if (CollUtil.isNotEmpty(pnrRmkList)) {
            iMnjxPnrRmkService.saveBatch(pnrRmkList);
        }
        if (CollUtil.isNotEmpty(nmRmkList)) {
            iMnjxNmRmkService.saveBatch(nmRmkList);
        }
        if (CollUtil.isNotEmpty(pnrTcList)) {
            iMnjxPnrTcService.saveBatch(pnrTcList);
        }
        if (CollUtil.isNotEmpty(nmTcList)) {
            iMnjxNmTcService.saveBatch(nmTcList);
        }
        if (CollUtil.isNotEmpty(pnrTkList)) {
            iMnjxPnrTkService.saveBatch(pnrTkList);
        }
        if (CollUtil.isNotEmpty(nmXnList)) {
            iMnjxNmXnService.saveBatch(nmXnList);
        }
        if (CollUtil.isNotEmpty(nmSsrList)) {
            iMnjxNmSsrService.saveBatch(nmSsrList);
        }
        if (CollUtil.isNotEmpty(nmFpList)) {
            iMnjxNmFpService.saveBatch(nmFpList);
        }
        if (CollUtil.isNotEmpty(nmFnList)) {
            iMnjxNmFnService.saveBatch(nmFnList);
        }
        if (CollUtil.isNotEmpty(nmFcList)) {
            iMnjxNmFcService.saveBatch(nmFcList);
        }
        if (CollUtil.isNotEmpty(nmOsiList)) {
            iMnjxNmOsiService.saveBatch(nmOsiList);
        }
        if (CollUtil.isNotEmpty(pnrFpList)) {
            iMnjxPnrFpService.saveBatch(pnrFpList);
        }
        if (CollUtil.isNotEmpty(pnrFnList)) {
            iMnjxPnrFnService.saveBatch(pnrFnList);
        }
        if (CollUtil.isNotEmpty(pnrFcList)) {
            iMnjxPnrFcService.saveBatch(pnrFcList);
        }
        if (CollUtil.isNotEmpty(pnrEiList)) {
            iMnjxPnrEiService.saveBatch(pnrEiList);
        }
        if (CollUtil.isNotEmpty(nmEiList)) {
            iMnjxNmEiService.saveBatch(nmEiList);
        }
        if (CollUtil.isNotEmpty(pnrAtList)) {
            iMnjxPnrAtService.saveBatch(pnrAtList);
        }
        if (CollUtil.isNotEmpty(pnrRecordList)) {
            iMnjxPnrRecordService.saveBatch(pnrRecordList);
        }
    }

    /**
     * 创建PNR基本信息
     *
     * @return PNR实体
     */
    private MnjxPnr createPnr() {
        MnjxPnr pnr = new MnjxPnr();
        // 生成PNR ID
        pnr.setPnrId(IdUtil.getSnowflake(1, 1).nextIdStr());
        // 生成PNR大编码和小编码
        pnr.setPnrIcs(this.generatePnrCode(MnjxPnr::getPnrIcs));
        pnr.setPnrCrs(this.generatePnrCode(MnjxPnr::getPnrCrs));
        // 设置PNR状态为未提交
        pnr.setPnrStatus("OP");
        // 设置创建时间
        pnr.setCreateTime(new Date());
        // 设置最大索引值
        pnr.setMaxIndex(0);
        pnr.setInterlink("0");
        UserInfo userInfo = iSguiCommonService.getCurrentUserInfo();
        if (userInfo != null) {
            pnr.setCreateSiId(userInfo.getSiId());
            pnr.setCreateOfficeNo(userInfo.getMnjxOffice().getOfficeNo());
        }
        return pnr;
    }

    /**
     * 生成PNR的大小编码
     *
     * @return 生成PNR的大小编码
     */
    private String generatePnrCode(SFunction<MnjxPnr, ?> column) {
        // 随机获取一个PNR编码
        String pnrCode = this.constPnrCode();
        // 设置标识量
        boolean isExist = true;
        // 循环当前编码是否存在
        while (isExist) {
            // 统计当前PNR编码是否存在了
            int count = iMnjxPnrService.lambdaQuery().eq(column, pnrCode).count();
            // 不存在就跳出循环
            if (count == Constant.ZERO) {
                isExist = false;
            }
            // 存在就重新获取一下
            else {
                pnrCode = this.constPnrCode();
            }
        }
        return pnrCode;
    }

    /**
     * Title: constPnrCode
     * Description: 生成PNR编码 格式：前2位英文，后4位英文数字混合组合
     *
     * @return {@link String}
     * <AUTHOR>
     * @date 2022/6/16 10:05
     */
    private String constPnrCode() {
        return StrUtil.format("{}{}", RandomUtil.randomString(Constant.LETTER, 2), RandomUtil.randomString(4)).toUpperCase();
    }

    /**
     * 处理航段信息
     *
     * @param flights    航段信息
     * @param pnr        PNR实体
     * @param pnrSegList 航段列表
     * @param pnrAtList  封口记录列表
     */
    private void processFlights(List<BookPnrDto.Flight> flights, MnjxPnr pnr, List<MnjxPnrSeg> pnrSegList, List<MnjxPnrAt> pnrAtList, Long psgNum, List<MnjxOpenCabin> updateOpenCabinList) throws SguiResultException {
        // 收集非ARNK航段的航空公司二字码，用于生成封口记录
        Set<String> airlineSet = new HashSet<>();
        int segmentNo = 1;

        String lastDst = "";
        if (flights.size() > 5) {
            throw new SguiResultException("当前联程最多支持5段");
        }
        for (BookPnrDto.Flight flight : flights) {
            if (CollUtil.isEmpty(flight.getSegments())) {
                continue;
            }

            for (BookPnrDto.Segment segment : flight.getSegments()) {
                MnjxPnrSeg pnrSeg = new MnjxPnrSeg();
                pnrSeg.setPnrSegId(IdUtil.getSnowflake(1, 1).nextIdStr());
                pnrSeg.setPnrId(pnr.getPnrId());
                pnrSeg.setPnrIndex(0); // 先设置为0，后续会重新排序
                pnrSeg.setPnrSegNo(segmentNo++);

                if (StrUtil.isNotEmpty(lastDst) && !lastDst.equals(segment.getDepartureAirport())) {
                    throw new SguiResultException("CHECK CONTINUITY");
                }
                lastDst = segment.getArrivalAirport();

                // 设置航段类型，SA表示缺口段
                if ("ARNK".equals(segment.getSegmentType())) {
                    pnrSeg.setPnrSegType("SA");
                    pnrSeg.setFlightNo("ARNK");
                    pnrSeg.setOrg(segment.getDepartureAirport());
                    pnrSeg.setDst(segment.getArrivalAirport());

                    // 构建SA航段的input_value
                    String inputValue = StrUtil.format("    ARNK             {}{}",
                            segment.getDepartureAirport(), segment.getArrivalAirport());
                    pnrSeg.setInputValue(inputValue);
                } else {
                    pnrSeg.setPnrSegType("SS");
                    pnrSeg.setFlightNo(segment.getMarketingAirline() + segment.getMarketingFlightNumber());
                    pnrSeg.setOrg(segment.getDepartureAirport());
                    pnrSeg.setDst(segment.getArrivalAirport());

                    // 收集非ARNK航段的航空公司二字码
                    airlineSet.add(segment.getMarketingAirline());
                    pnrSeg.setFlightDate(segment.getDepartureDate());
                    pnrSeg.setEstimateOff(segment.getDepartureTime().replace(":", ""));
                    pnrSeg.setEstimateArr(segment.getArrivalTime().replace(":", ""));

                    // 校验航班是否有效，前端在修改航班重新查询之后没有更新，可能会带过来不匹配的航班号和出发到达航站、时间
                    List<MnjxPlanSection> planSectionList = iSguiCommonService.getPlanSectionListByFlightNo(pnrSeg.getFlightNo(), pnrSeg.getFlightDate());
                    if (CollUtil.isEmpty(planSectionList)) {
                        throw new SguiResultException("修改的航班" + pnrSeg.getFlightNo() + "不存在");
                    }
                    MnjxAirport orgAirport = iMnjxAirportService.lambdaQuery()
                            .eq(MnjxAirport::getAirportCode, pnrSeg.getOrg())
                            .one();
                    MnjxAirport dstAirport = iMnjxAirportService.lambdaQuery()
                            .eq(MnjxAirport::getAirportCode, pnrSeg.getDst())
                            .one();
                    if (planSectionList.size() == 1) {
                        if (!planSectionList.get(0).getDepAptId().equals(orgAirport.getAirportId()) || !planSectionList.get(0).getArrAptId().equals(dstAirport.getAirportId())) {
                            throw new SguiResultException("修改的航班" + pnrSeg.getFlightNo() + "不存在");
                        }
                    } else {
                        boolean match = false;
                        boolean findDep = false;
                        boolean findArr = false;
                        for (MnjxPlanSection planSection : planSectionList) {
                            if (planSection.getDepAptId().equals(orgAirport.getAirportId()) && planSection.getArrAptId().equals(dstAirport.getAirportId()) && planSection.getEstimateOff().equals(segment.getDepartureTime().replace(":", "")) && planSection.getEstimateArr().equals(segment.getArrivalTime().replace(":", ""))) {
                                match = true;
                                break;
                            } else if (planSection.getDepAptId().equals(orgAirport.getAirportId()) && planSection.getEstimateOff().equals(segment.getDepartureTime().replace(":", ""))) {
                                findDep = true;
                            } else if (planSection.getArrAptId().equals(dstAirport.getAirportId()) && findDep && planSection.getEstimateArr().equals(segment.getArrivalTime().replace(":", ""))) {
                                findArr = true;
                            }
                            if (findArr) {
                                match = true;
                                break;
                            }
                        }
                        if (!match) {
                            throw new SguiResultException("修改的航班" + pnrSeg.getFlightNo() + "不存在");
                        }
                    }

                    List<MnjxOpenCabin> openCabinList = iSguiCommonService.getOpenCabinListByFlightNo(StrUtil.isNotEmpty(segment.getSharedInfo()) ? segment.getSharedInfo() : pnrSeg.getFlightNo(), pnrSeg.getFlightDate(), pnrSeg.getOrg(), pnrSeg.getDst());
                    boolean seatAvailable = openCabinList.stream()
                            .anyMatch(o -> o.getSellCabin().equals(segment.getCabinCode()) && o.getSeatAvailable() >= psgNum);
                    if (!seatAvailable) {
                        throw new SguiResultException("所选舱位座位数不足");
                    }
                    openCabinList.stream()
                            .filter(o -> o.getSellCabin().equals(segment.getCabinCode()))
                            .forEach(k -> {
                                int availableNumber = k.getSeatAvailable() - psgNum.intValue();
                                k.setSeatAvailable(availableNumber);
                                updateOpenCabinList.add(k);
                            });
                    pnrSeg.setCabinClass(openCabinList.get(0).getCabinClass());
                    pnrSeg.setSellCabin(segment.getCabinCode());
                    pnrSeg.setActionCode("HK");
                    pnrSeg.setSeatNumber(psgNum.intValue());
                    pnrSeg.setPlaneVersion(segment.getMarketingPlaneType());
                    pnrSeg.setRecreation("E");
                    pnrSeg.setCarrierFlight(segment.getSharedInfo());

                    // 构建普通航段的input_value
                    // 格式："  CA1836 N   MO12MAY25  PVGPEK HK1   1850 2110          330 M 0  R E T1T2"
                    String weekDay = DateUtils.ymd2WeekEn(pnrSeg.getFlightDate()).substring(0, 2);

                    String inputValue = StrUtil.format("  {} {}   {}{}  {}{} {}{}   {} {}          {} M 0  R E {}{}",
                            pnrSeg.getFlightNo(), pnrSeg.getSellCabin(),
                            weekDay, DateUtils.ymd2Com(pnrSeg.getFlightDate()),
                            pnrSeg.getOrg(), pnrSeg.getDst(), pnrSeg.getActionCode(), pnrSeg.getSeatNumber(),
                            pnrSeg.getEstimateOff(), pnrSeg.getEstimateArr(),
                            segment.getMarketingPlaneType(), segment.getDepartureTerminal(), segment.getArrivalTerminal());

                    pnrSeg.setInputValue(inputValue);
                }

                // 添加到列表中
                pnrSegList.add(pnrSeg);
            }
        }

        if (pnrSegList.get(0).getPnrSegType().equals("SA")) {
            throw new SguiResultException("首航段不允许ARNK");
        }

        // 生成封口记录
        // 第一条封口记录，at_type为空
        MnjxPnrAt pnrAt = new MnjxPnrAt();
        pnrAt.setPnrAtId(IdUtil.getSnowflake(1, 1).nextIdStr());
        pnrAt.setPnrId(pnr.getPnrId());
        pnrAt.setAtNo("001"); // 第一次封口
        pnrAt.setAtDateTime(new Date()); // 当前时间
        pnrAt.setAtSiId(iSguiCommonService.getCurrentUserInfo().getSiId());
        pnrAtList.add(pnrAt);

        // 根据非ARNK航段的航空公司二字码生成封口记录
        for (String airline : airlineSet) {
            MnjxPnrAt airlinePnrAt = new MnjxPnrAt();
            airlinePnrAt.setPnrAtId(IdUtil.getSnowflake(1, 1).nextIdStr());
            airlinePnrAt.setPnrId(pnr.getPnrId());
            airlinePnrAt.setAtNo("001"); // 第一次封口
            airlinePnrAt.setAtType(airline); // 航空公司二字码
            airlinePnrAt.setAtDateTime(new Date()); // 当前时间
            airlinePnrAt.setAtSiId(iSguiCommonService.getCurrentUserInfo().getSiId());
            pnrAtList.add(airlinePnrAt);
        }
    }

    /**
     * 获取月份缩写
     *
     * @param month 月份
     * @return 月份缩写
     */
    private String getMonthAbbr(String month) {
        switch (month) {
            case "01":
                return "JAN";
            case "02":
                return "FEB";
            case "03":
                return "MAR";
            case "04":
                return "APR";
            case "05":
                return "MAY";
            case "06":
                return "JUN";
            case "07":
                return "JUL";
            case "08":
                return "AUG";
            case "09":
                return "SEP";
            case "10":
                return "OCT";
            case "11":
                return "NOV";
            case "12":
                return "DEC";
            default:
                return "";
        }
    }

    /**
     * 处理旅客证件信息
     *
     * @param passenger 旅客信息
     * @param pnrNm     旅客实体
     * @param nmSsrList 特服列表
     */
    private void processPassengerDocument(BookPnrDto.Passenger passenger, MnjxPnrNm pnrNm, List<MnjxNmSsr> nmSsrList, List<MnjxPnrSeg> pnrSegList) {
        // 如果是身份证，生成FOID特服
        if ("NI_I".equals(passenger.getCertificateType()) && StrUtil.isNotEmpty(passenger.getCertificateNo())) {
            String airlineCode = pnrSegList.get(0).getFlightNo().substring(0, 2);
            MnjxNmSsr nmSsr = new MnjxNmSsr();
            nmSsr.setNmSsrId(IdUtil.getSnowflake(1, 1).nextIdStr());
            nmSsr.setPnrNmId(pnrNm.getPnrNmId());
            nmSsr.setPnrIndex(0); // 先设置为0，后续会重新排序
            nmSsr.setSsrType("FOID");
            nmSsr.setActionCode("HK");
            nmSsr.setAirlineCode(airlineCode);

            // 格式："SSR FOID CZ HK1 NI123123201812121311/P1"
            String ssrInfo = StrUtil.format("SSR FOID {} HK1 NI{}/P{}",
                    airlineCode, passenger.getCertificateNo(), pnrNm.getPsgIndex());

            nmSsr.setSsrInfo(ssrInfo);
            nmSsr.setInputValue(ssrInfo);

            // 添加到列表中
            nmSsrList.add(nmSsr);
        } else if ("UU".equals(passenger.getCertificateType()) && StrUtil.isNotEmpty(passenger.getCertificateNo())) {
            String airlineCode = pnrSegList.get(0).getFlightNo().substring(0, 2);
            MnjxNmSsr nmSsr = new MnjxNmSsr();
            nmSsr.setNmSsrId(IdUtil.getSnowflake(1, 1).nextIdStr());
            nmSsr.setPnrNmId(pnrNm.getPnrNmId());
            nmSsr.setPnrIndex(0); // 先设置为0，后续会重新排序
            nmSsr.setSsrType("FOID");
            nmSsr.setActionCode("HK");
            nmSsr.setAirlineCode(airlineCode);

            // 格式："SSR FOID CZ HK1 UU123123201812121311/P1"
            String ssrInfo = StrUtil.format("SSR FOID {} HK1 UU{}/P{}",
                    airlineCode, passenger.getCertificateNo(), pnrNm.getPsgIndex());

            nmSsr.setSsrInfo(ssrInfo);
            nmSsr.setInputValue(ssrInfo);

            // 添加到列表中
            nmSsrList.add(nmSsr);
        } else if (passenger.getCertificateType().startsWith("PP_") && StrUtil.isNotEmpty(passenger.getCertificateNo())) {
            String airlineCode = pnrSegList.get(0).getFlightNo().substring(0, 2);
            MnjxNmSsr nmSsr = new MnjxNmSsr();
            nmSsr.setNmSsrId(IdUtil.getSnowflake(1, 1).nextIdStr());
            nmSsr.setPnrNmId(pnrNm.getPnrNmId());
            nmSsr.setPnrIndex(0); // 先设置为0，后续会重新排序
            nmSsr.setSsrType("DOCS");
            nmSsr.setActionCode("HK");
            nmSsr.setAirlineCode(airlineCode);

            // 格式：SSR DOCS CA HK1 P/CN/32322110/CN/10MAY20/M/22MAY22/ZHANG/SAN/H/P1
            String ssrInfo = StrUtil.format("SSR DOCS {} HK1 {}/{}/{}/{}/{}/{}/{}/{}{}/P{}",
                    airlineCode, passenger.getCertificateType().split("_")[1], passenger.getCertificateIssueCountry(), passenger.getCertificateNo(), passenger.getCertificateIssueCountry(),
                    DateUtils.ymd2Com(passenger.getBirthday()), passenger.getSex(), DateUtils.ymd2Com(passenger.getCertificateExpiryDate()),
                    passenger.getDocsName(), StrUtil.isNotEmpty(passenger.getHolder()) ? "/H" : "", pnrNm.getPsgIndex());

            nmSsr.setSsrInfo(ssrInfo);
            nmSsr.setInputValue(ssrInfo);

            // 添加到列表中
            nmSsrList.add(nmSsr);
        }
    }

    /**
     * 处理孩童特服
     *
     * @param passenger 旅客信息
     * @param pnrNm     旅客实体
     * @param nmSsrList 特服列表
     */
    private void processChildSpecialService(BookPnrDto.Passenger passenger, MnjxPnrNm pnrNm, List<MnjxNmSsr> nmSsrList, String airlineCode) {

        MnjxNmSsr nmSsr = new MnjxNmSsr();
        nmSsr.setNmSsrId(IdUtil.getSnowflake(1, 1).nextIdStr());
        nmSsr.setPnrNmId(pnrNm.getPnrNmId());
        nmSsr.setPnrIndex(0); // 先设置为0，后续会重新排序
        nmSsr.setSsrType("CHLD");
        nmSsr.setActionCode("HK");
        nmSsr.setAirlineCode(airlineCode);

        // 格式："SSR CHLD CZ HK1 12DEC18/P1"
        String ssrInfo = StrUtil.format("SSR CHLD {} HK1 {}/P{}",
                airlineCode, DateUtils.ymd2Com(passenger.getBirthday()), pnrNm.getPsgIndex());

        nmSsr.setSsrInfo(ssrInfo);
        nmSsr.setInputValue(ssrInfo);

        // 添加到列表中
        nmSsrList.add(nmSsr);
    }

    /**
     * 处理默认备注信息
     *
     * @param dto
     * @param pnr
     * @param pnrSegList
     * @param pnrRmkList
     * @param pnrEiList
     * @param nmEiList
     * @param pnrNmList
     * @param nmFcList
     * @param pnrFcList
     */
    private void processDefaultRemarks(BookPnrDto dto, MnjxPnr pnr, List<MnjxPnrSeg> pnrSegList, List<MnjxPnrRmk> pnrRmkList, List<MnjxPnrEi> pnrEiList, List<MnjxNmEi> nmEiList,
                                       List<MnjxPnrNm> pnrNmList, List<MnjxNmFc> nmFcList, List<MnjxPnrFc> pnrFcList) {
        // 1. 生成RMK CA/ZAJX1W备注
        if (CollUtil.isNotEmpty(pnrSegList)) {
            MnjxPnrSeg firstSegment = pnrSegList.get(0);
            String airlineCode = firstSegment.getFlightNo().substring(0, 2);

            MnjxPnrRmk pnrRmk = new MnjxPnrRmk();
            pnrRmk.setPnrRmkId(IdUtil.getSnowflake(1, 1).nextIdStr());
            pnrRmk.setPnrId(pnr.getPnrId());
            pnrRmk.setPnrIndex(0); // 先设置为0，后续会重新排序
            pnrRmk.setRmkName("RMK");

            String rmkInfo = StrUtil.format("RMK {}/{}", airlineCode, pnr.getPnrIcs());
            pnrRmk.setRmkInfo(rmkInfo);

            // 构建输入值
            pnrRmk.setInputValue(rmkInfo);

            // 添加到列表中
            pnrRmkList.add(pnrRmk);
        }

        // 2. 如果有运价信息，生成相关备注和EI信息
        if (!Boolean.TRUE.equals(dto.getNotNeedWritePrice()) && CollUtil.isNotEmpty(dto.getPrice())) {
            // 2.1 生成RMK CMS/A/**备注
            MnjxPnrRmk cmsaRmk = new MnjxPnrRmk();
            cmsaRmk.setPnrRmkId(IdUtil.getSnowflake(1, 1).nextIdStr());
            cmsaRmk.setPnrId(pnr.getPnrId());
            cmsaRmk.setPnrIndex(0); // 先设置为0，后续会重新排序
            cmsaRmk.setRmkName("CMSA");
            cmsaRmk.setRmkInfo("RMK CMS/A/**");
            cmsaRmk.setInputValue("RMK CMS/A/**");
            pnrRmkList.add(cmsaRmk);

            // 2.2 生成RMK AUTOMATIC FARE QUOTE备注
            MnjxPnrRmk freeRmk = new MnjxPnrRmk();
            freeRmk.setPnrRmkId(IdUtil.getSnowflake(1, 1).nextIdStr());
            freeRmk.setPnrId(pnr.getPnrId());
            freeRmk.setPnrIndex(0); // 先设置为0，后续会重新排序
            freeRmk.setRmkName("FREE");
            freeRmk.setRmkInfo("RMK AUTOMATIC FARE QUOTE");
            freeRmk.setInputValue("RMK AUTOMATIC FARE QUOTE");
            pnrRmkList.add(freeRmk);

            // 2.3 生成EI/Q/改退收费信息
            if (CollUtil.isNotEmpty(nmFcList)) {
                for (MnjxNmFc nmFc : nmFcList) {
                    MnjxPnrNm pnrNm = pnrNmList.stream()
                            .filter(p -> nmFc.getPnrNmId().equals(p.getPnrNmId()))
                            .collect(Collectors.toList())
                            .get(0);
                    MnjxNmEi nmEi = new MnjxNmEi();
                    nmEi.setNmEiId(IdUtil.getSnowflake(1, 1).nextIdStr());
                    nmEi.setPnrNmId(pnrNm.getPnrNmId());
                    nmEi.setPnrIndex(0); // 先设置为0，后续会重新排序

                    // 获取改退收费信息
                    String eiInfo = StrUtil.format("EI/Q/GAITUISHOUFEI改退收费/P{}", pnrNm.getPsgIndex());
                    if (nmFc.getIsBaby() == 1) {
                        eiInfo = StrUtil.format("EI/IN/Q/GAITUISHOUFEI改退收费/P{}", pnrNm.getPsgIndex());
                    }
                    nmEi.setEiInfo(eiInfo);
                    nmEi.setInputValue(eiInfo);
                    nmEiList.add(nmEi);
                }
            }
            if (CollUtil.isNotEmpty(pnrFcList)) {
                for (MnjxPnrFc pnrFc : pnrFcList) {
                    MnjxPnrEi pnrEi = new MnjxPnrEi();
                    pnrEi.setPnrEiId(IdUtil.getSnowflake(1, 1).nextIdStr());
                    pnrEi.setPnrId(pnrFc.getPnrId());
                    pnrEi.setPnrIndex(0); // 先设置为0，后续会重新排序

                    // 获取改退收费信息
                    String eiInfo = "EI/Q/GAITUISHOUFEI改退收费";
                    if (pnrFc.getIsBaby() == 1) {
                        eiInfo = "EI/IN/Q/GAITUISHOUFEI改退收费";
                    }
                    pnrEi.setEiInfo(eiInfo);
                    pnrEi.setInputValue(eiInfo);
                    pnrEiList.add(pnrEi);
                }
            }
        }
    }

    /**
     * 处理婴儿特服信息
     *
     * @param passenger  婴儿旅客信息
     * @param adultPnrNm 关联的成人旅客实体
     * @param nmSsrList  特服列表
     * @param nmOsiList  OSI列表
     * @param pnrSegList 航段列表
     */
    private void processInfantSpecialService(BookPnrDto.Passenger passenger, MnjxPnrNm adultPnrNm, List<MnjxNmSsr> nmSsrList, List<MnjxNmOsi> nmOsiList, List<MnjxPnrSeg> pnrSegList) {
        // 解析婴儿姓名
        String[] nameParts = passenger.getFullName().split("/");
        String lastName = nameParts.length > 0 ? nameParts[0] : "";
        String firstName = nameParts.length > 1 ? nameParts[1] : "";
        String fullName = lastName + "/" + firstName;

        // 生成婴儿OSI信息
        // 格式："OSI YY 1INF YING/ER INF/P1"
        MnjxNmOsi nmOsi = new MnjxNmOsi();
        nmOsi.setPnrOsiId(IdUtil.getSnowflake(1, 1).nextIdStr());
        nmOsi.setPnrNmId(adultPnrNm.getPnrNmId());
        nmOsi.setPnrIndex(0); // 先设置为0，后续会重新排序

        String osiInfo = StrUtil.format("OSI YY 1INF {} INF/P{}", fullName, adultPnrNm.getPsgIndex());
        nmOsi.setPnrOsiInfo(osiInfo);
        nmOsi.setInputValue(osiInfo);

        // 添加到列表中
        nmOsiList.add(nmOsi);

        // 处理婴儿特服信息
        for (BookPnrDto.Segment segment : passenger.getInftSelectedSegments()) {
            if ("ARNK".equals(segment.getMarketingFlightNumber())) {
                continue;
            }
            MnjxNmSsr nmSsr = new MnjxNmSsr();
            nmSsr.setNmSsrId(IdUtil.getSnowflake(1, 1).nextIdStr());
            nmSsr.setPnrNmId(adultPnrNm.getPnrNmId());
            nmSsr.setPnrIndex(0); // 先设置为0，后续会重新排序
            nmSsr.setSsrType("INFT");
            nmSsr.setActionCode("KK"); // 婴儿特服的actionCode为KK
            nmSsr.setAirlineCode(segment.getMarketingAirline());
            nmSsr.setOrgDst(segment.getDepartureAirport() + segment.getArrivalAirport());
            Integer pnrSegNo = pnrSegList.stream()
                    .filter(p -> StrUtil.isNotEmpty(p.getFlightNo()) && p.getFlightNo().equals(segment.getMarketingAirline() + segment.getMarketingFlightNumber()))
                    .collect(Collectors.toList())
                    .get(0)
                    .getPnrSegNo();
            nmSsr.setPnrSegNo(pnrSegNo);
            nmSsr.setFltDate(segment.getDepartureDate());

            // 使用上面解析的婴儿姓名

            // 格式："SSR INFT CZ KK1 SHAPKX 8880 N21MAY YING/ER 01MAR24/P1"
            String ssrInfo = StrUtil.format("SSR INFT {} KK1 {}{} {} {}{} {}/{} {}/P{}",
                    segment.getMarketingAirline(), segment.getDepartureAirport(), segment.getArrivalAirport(),
                    segment.getMarketingFlightNumber(), segment.getCabinCode(), DateUtils.ymd2Com(segment.getDepartureDate()),
                    lastName, firstName, DateUtils.ymd2Com(passenger.getBirthday()), adultPnrNm.getPsgIndex());

            nmSsr.setSsrInfo(ssrInfo);
            nmSsr.setInputValue(ssrInfo);

            // 添加到列表中
            nmSsrList.add(nmSsr);
        }

        // 如果请求参数certificateType不为空，说明需要同时添加婴儿证件信息
        if (StrUtil.isNotEmpty(passenger.getCertificateType())) {
            MnjxNmSsr xnCertificateSsr = new MnjxNmSsr();
            // SSR DOCS CA HK1 I/CN/ABC12345/CN/08MAY24/FI/17OCT25/AN/YINERINF/P2
            xnCertificateSsr.setNmSsrId(IdUtil.getSnowflake(1, 1).nextIdStr());
            xnCertificateSsr.setPnrNmId(adultPnrNm.getPnrNmId());
            xnCertificateSsr.setPnrIndex(0); // 先设置为0，后续会重新排序
            xnCertificateSsr.setSsrType("DOCS");
            xnCertificateSsr.setActionCode("HK");
            String airlineCode = passenger.getInftSelectedSegments().get(0).getMarketingAirline();
            xnCertificateSsr.setAirlineCode(airlineCode);

            String ssrInfo = StrUtil.format("SSR DOCS {} HK1 {}/{}/{}/{}/{}/{}I/{}/{}{}/P{}",
                    airlineCode, passenger.getCertificateType().split("_")[1], passenger.getCertificateIssueCountry(), passenger.getCertificateNo(), passenger.getCertificateIssueCountry(),
                    DateUtils.ymd2Com(passenger.getBirthday()), passenger.getSex(), StrUtil.isNotEmpty(passenger.getCertificateExpiryDate()) ? DateUtils.ymd2Com(passenger.getCertificateExpiryDate()) : "",
                    passenger.getFullName(), StrUtil.isNotEmpty(passenger.getHolder()) ? "/H" : "", adultPnrNm.getPsgIndex());

            xnCertificateSsr.setSsrInfo(ssrInfo);
            xnCertificateSsr.setInputValue(ssrInfo);

            // 添加到列表中
            nmSsrList.add(xnCertificateSsr);
        }
    }

    /**
     * 处理旅客信息
     *
     * @param passengers 旅客信息
     * @param pnr        PNR实体
     * @param pnrNmList  旅客列表
     * @param nmXnList   婴儿列表
     * @param nmSsrList  特服列表
     * @param nmOsiList  OSI列表
     * @param pnrSegList 航段列表
     * @return 旅客ID与PNR序号的映射
     */
    private Map<String, Integer> processPassengers(List<BookPnrDto.Passenger> passengers, MnjxPnr pnr,
                                                   List<MnjxPnrNm> pnrNmList, List<MnjxNmXn> nmXnList,
                                                   List<MnjxNmSsr> nmSsrList, List<MnjxNmOsi> nmOsiList,
                                                   List<MnjxPnrSeg> pnrSegList, List<MnjxNmRmk> nmRmkList) throws SguiResultException {
        Map<String, Integer> passengerIndexMap = new HashMap<>();
        int psgIndex = 1;

        String firstSegAirlineCode = pnrSegList.get(0).getFlightNo().substring(0, 2);

        Map<String, String> adtInfMap = new HashMap<>();

        // 先处理非婴儿旅客
        for (BookPnrDto.Passenger passenger : passengers) {
            if ("INF".equals(passenger.getPassengerType())) {
                continue;
            }

            // 创建旅客信息
            MnjxPnrNm pnrNm = new MnjxPnrNm();
            pnrNm.setPnrNmId(IdUtil.getSnowflake(1, 1).nextIdStr());
            pnrNm.setPnrId(pnr.getPnrId());
            pnrNm.setPnrIndex(psgIndex); // 先设置为psgIndex，后续会重新排序
            pnrNm.setPsgIndex(psgIndex++);

            // 如果旅客勾选了VIP
            if ("VIP".equals(passenger.getVipType())) {
                pnrNm.setPsgInd("VIP");
                if (CollUtil.isNotEmpty(passenger.getVipTextList())) {
                    for (String vipText : passenger.getVipTextList()) {
                        if (!vipText.contains(" ")) {
                            vipText = vipText.substring(0, 2) + " " + vipText.substring(2);
                        }
                        String[] vipTextSplit = vipText.split(" ");
                        // 验证航司
                        String vipAirlineCode = vipTextSplit[0];
                        if (pnrSegList.stream().noneMatch(p -> p.getFlightNo().substring(0, 2).equals(vipAirlineCode))) {
                            throw new SguiResultException("AIRLINE");
                        }
                        // 构建OSI的vip信息
                        MnjxNmOsi vipOsi = new MnjxNmOsi();
                        vipOsi.setPnrOsiId(IdUtil.getSnowflake(1, 1).nextIdStr());
                        vipOsi.setPnrNmId(pnrNm.getPnrNmId());
                        vipOsi.setPnrIndex(0); // 先设置为0，后续会重新排序
                        vipOsi.setPnrOsiType("VIP");
                        vipText = "OSI " + vipText + "/P" + pnrNm.getPsgIndex();
                        vipOsi.setPnrOsiInfo(vipText);
                        vipOsi.setInputValue(vipText);
                        nmOsiList.add(vipOsi);
                    }
                }
            }

            // 设置旅客类型
            if ("CHD".equals(passenger.getPassengerType())) {
                pnrNm.setPsgType("1"); // 儿童
            } else if ("UM".equals(passenger.getPassengerType())) {
                pnrNm.setPsgType("2"); // 无人陪伴旅客
            } else if (StrUtil.isNotEmpty(passenger.getInfantRef())) {
                pnrNm.setPsgType("3"); // 带婴儿的旅客
            } else {
                pnrNm.setPsgType("0"); // 成人
            }
            if (StrUtil.isNotEmpty(passenger.getSpecialPassengerType()) && StrUtil.equalsAny(passenger.getSpecialPassengerType(), "GM", "JC", "GMJC")) {
                // 同时生成军警残的RMK
                MnjxNmRmk nmRmk = new MnjxNmRmk();
                nmRmk.setNmRmkId(IdUtil.getSnowflake(1, 1).nextIdStr());
                nmRmk.setPnrNmId(pnrNm.getPnrNmId());
                nmRmk.setPnrIndex(0); // 先设置为0，后续会重新排序
                nmRmk.setRmkName("GMJC");
                String inputValue = "RMK GMJC " + passenger.getIdentityText() + "/P" + pnrNm.getPsgIndex();
                nmRmk.setRmkInfo(inputValue);
                nmRmk.setInputValue(inputValue);

                // 添加到列表中
                nmRmkList.add(nmRmk);
                // 是否勾选了DFMM DFPP
                if (CollUtil.isNotEmpty(passenger.getSupplementaryIdentityInfoList())) {
                    List<BookPnrDto.SupplementaryIdentityInfo> collect = passenger.getSupplementaryIdentityInfoList().stream()
                            .filter(s -> "CKIN".equals(s.getType()) && StrUtil.equalsAny(s.getText(), "DFMM", "DFPP"))
                            .collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(collect)) {
                        BookPnrDto.SupplementaryIdentityInfo supplementaryIdentityInfo = collect.get(0);
                        // 构建SSR CKIN
                        // SSR CKIN CA DFMM HK1/P1
                        MnjxNmSsr nmSsr = new MnjxNmSsr();
                        nmSsr.setNmSsrId(IdUtil.getSnowflake(1, 1).nextIdStr());
                        nmSsr.setPnrNmId(pnrNm.getPnrNmId());
                        nmSsr.setPnrIndex(0); // 先设置为0，后续会重新排序
                        nmSsr.setSsrType("CKIN");
                        nmSsr.setActionCode("HK");
                        nmSsr.setAirlineCode(firstSegAirlineCode);
                        String ssrInfo = StrUtil.format("SSR CKIN {} {} HK1/P{}", firstSegAirlineCode, supplementaryIdentityInfo.getText(), pnrNm.getPsgIndex());
                        nmSsr.setSsrInfo(ssrInfo);
                        nmSsr.setInputValue(ssrInfo);

                        // 添加到列表中
                        nmSsrList.add(nmSsr);
                    }
                }
            }

            // 设置旅客姓名
            pnrNm.setName(passenger.getFullName());

            // 设置性别
            pnrNm.setSex(passenger.getSex());

            // 设置查询用姓名
            // 判断是否中文姓名
            // fullName如果有中文需要去除所有的非中文
            String replaceName = ReUtil.replaceAll(passenger.getFullName(), "[^\\u4e00-\\u9fa5]+", "");
            if (replaceName.matches("[\\u4e00-\\u9fa5]+")) {
                pnrNm.setIsCnin("CNIN");
                pnrNm.setQueryName(PinyinUtils.getPinYin(replaceName, false));
            } else {
                pnrNm.setQueryName(replaceName);
            }

            // 构建输入值
            String inputValue = passenger.getFullName();
            if ("CHD".equals(passenger.getPassengerType()) && !inputValue.contains("CHD")) {
                inputValue += " CHD";
            }
            pnrNm.setInputValue(inputValue);

            // 添加到列表中
            pnrNmList.add(pnrNm);

            // 记录旅客ID与PNR序号的映射
            passengerIndexMap.put(passenger.getId(), pnrNm.getPnrIndex());

            // 处理证件信息
            if (StrUtil.isNotEmpty(passenger.getCertificateType()) && StrUtil.isNotEmpty(passenger.getCertificateNo())) {
                this.processPassengerDocument(passenger, pnrNm, nmSsrList, pnrSegList);
            } else {
                throw new SguiResultException("PLEASE INPUT VALID IDENTITY INFORMATION");
            }

            // 处理孩童特服
            if ("CHD".equals(passenger.getPassengerType()) && StrUtil.isNotEmpty(passenger.getBirthday())) {
                this.processChildSpecialService(passenger, pnrNm, nmSsrList, firstSegAirlineCode);
            }

            // 处理旅客联系方式
            if (CollUtil.isNotEmpty(passenger.getContactAndCommunicationList())) {
                this.processPassengerContacts(passenger.getContactAndCommunicationList(), pnrNm, nmSsrList, nmOsiList, firstSegAirlineCode);
            }

            // 处理婴儿联系关系
            if (StrUtil.isNotEmpty(passenger.getInfantRef())) {
                adtInfMap.put(passenger.getInfantRef(), passenger.getId());
            }

            // 处理常旅客
            if (CollUtil.isNotEmpty(passenger.getFrequenters())) {
                // 暂时不支持常旅客
                throw new SguiResultException("INVLID PROFILE NUMBER");
            }
        }

        // 再处理婴儿旅客
        for (BookPnrDto.Passenger passenger : passengers) {
            if (!"INF".equals(passenger.getPassengerType())) {
                continue;
            }

            // 找到关联的成人旅客
            if (!adtInfMap.containsKey(passenger.getId())) {
                continue;
            }

            String adtId = adtInfMap.get(passenger.getId());
            // 获取关联的成人旅客信息
            Integer adultPnrIndex = passengerIndexMap.get(adtId);
            MnjxPnrNm adultPnrNm = null;

            // 从列表中查找成人旅客
            for (MnjxPnrNm nm : pnrNmList) {
                if (nm.getPnrIndex().equals(adultPnrIndex)) {
                    adultPnrNm = nm;
                    break;
                }
            }

            if (adultPnrNm == null) {
                continue;
            }

            // 创建婴儿信息
            MnjxNmXn nmXn = new MnjxNmXn();
            nmXn.setNmXnId(IdUtil.getSnowflake(1, 1).nextIdStr());
            nmXn.setPnrNmId(adultPnrNm.getPnrNmId());
            nmXn.setPnrIndex(0); // 先设置为0，后续会重新排序

            // 设置婴儿姓名
            nmXn.setXnCname(passenger.getFullName());
            nmXn.setXnFullName(passenger.getInfantChineseName());

            // 设置婴儿生日
            nmXn.setXnBirthday(passenger.getBirthday().substring(0, 7));
            nmXn.setGender(passenger.getSex());

            // 构建输入值
            // 格式："XN/IN/XING/MING INF(JUN23)/P1"
            String[] nameParts = passenger.getFullName().split("/");
            String lastName = nameParts.length > 0 ? nameParts[0] : "";
            String firstName = nameParts.length > 1 ? nameParts[1] : "";

            String birthMonth = passenger.getBirthday().substring(5, 7);
            String birthMonthAbbr = this.getMonthAbbr(birthMonth);
            String birthYear = passenger.getBirthday().substring(2, 4);
            String inputValue = StrUtil.format("XN/IN/{}/{} INF({}{})/P{}",
                    lastName, firstName, birthMonthAbbr, birthYear, adultPnrNm.getPsgIndex());
            nmXn.setInputValue(inputValue);

            // 添加到列表中
            nmXnList.add(nmXn);

            if (CollUtil.isEmpty(passenger.getInftSelectedSegments())
                    || passenger.getInftSelectedSegments().stream().filter(s -> !"ARNK".equals(s.getSegmentType())).count() != pnrSegList.stream().filter(s -> !"SA".equals(s.getPnrSegType())).count()) {
                throw new SguiResultException("PLEASE INPUT SSR INFT AND NEED AIRLINE CONFIRM IT");
            }

            // 处理婴儿特服信息
            this.processInfantSpecialService(passenger, adultPnrNm, nmSsrList, nmOsiList, pnrSegList);
        }

        return passengerIndexMap;
    }

    /**
     * 处理旅客联系方式
     *
     * @param contacts  联系方式列表
     * @param pnrNm     旅客信息
     * @param nmSsrList 特服列表
     * @param nmOsiList OSI列表
     */
    private void processPassengerContacts(List<BookPnrDto.ContactAndCommunication> contacts, MnjxPnrNm pnrNm,
                                          List<MnjxNmSsr> nmSsrList, List<MnjxNmOsi> nmOsiList, String airlineCode) {
        for (BookPnrDto.ContactAndCommunication contact : contacts) {
            if (StrUtil.startWith(contact.getType(), "OSI")) {
                // 创建旅客OSI信息
                MnjxNmOsi nmOsi = new MnjxNmOsi();
                nmOsi.setPnrOsiId(IdUtil.getSnowflake(1, 1).nextIdStr());
                nmOsi.setPnrNmId(pnrNm.getPnrNmId());
                nmOsi.setPnrIndex(0); // 先设置为0，后续会重新排序

                // 解析OSI类型
                String[] osiParts = contact.getType().split(" ");
                if (osiParts.length > 1) {
                    nmOsi.setPnrOsiType(osiParts[1]);
                }

                // 设置联系信息
                String osiInfo = StrUtil.format("OSI {} {} {}/P{}", airlineCode, osiParts[1], contact.getText(), pnrNm.getPsgIndex());
                nmOsi.setPnrOsiInfo(osiInfo);
                nmOsi.setInputValue(osiInfo);

                // 添加到列表中
                nmOsiList.add(nmOsi);
            } else if (StrUtil.startWith(contact.getType(), "SSR")) {
                // 创建旅客SSR信息
                MnjxNmSsr nmSsr = new MnjxNmSsr();
                nmSsr.setNmSsrId(IdUtil.getSnowflake(1, 1).nextIdStr());
                nmSsr.setPnrNmId(pnrNm.getPnrNmId());
                nmSsr.setPnrIndex(0); // 先设置为0，后续会重新排序

                // 解析SSR类型
                String[] ssrParts = contact.getType().split(" ");
                if (ssrParts.length > 1) {
                    nmSsr.setSsrType(ssrParts[1]);
                    if (ssrParts.length > 2) {
                        nmSsr.setAirlineCode(ssrParts[2]);
                    }
                }

                nmSsr.setActionCode("HK");

                // 设置联系信息
                String ssrInfo = StrUtil.format("{} {} HK1 {}/P{}", contact.getType(), airlineCode, contact.getText(), pnrNm.getPsgIndex());
                nmSsr.setSsrInfo(ssrInfo);
                nmSsr.setInputValue(ssrInfo);

                // 添加到列表中
                nmSsrList.add(nmSsr);
            }
        }
    }

    /**
     * 处理联系信息
     *
     * @param contactInfo 联系信息
     * @param pnr         PNR实体
     * @param pnrCtList   联系方式列表
     * @param pnrOsiList  OSI列表
     */
    private void processContactInfo(BookPnrDto.ContactInfo contactInfo, MnjxPnr pnr, List<MnjxPnrCt> pnrCtList, List<MnjxPnrOsi> pnrOsiList, String airlineCode) {
        // 处理联系方式列表
        if (CollUtil.isNotEmpty(contactInfo.getCtList())) {
            for (String ctText : contactInfo.getCtList()) {
                MnjxPnrCt pnrCt = new MnjxPnrCt();
                pnrCt.setPnrCtId(IdUtil.getSnowflake(1, 1).nextIdStr());
                pnrCt.setPnrId(pnr.getPnrId());
                pnrCt.setPnrIndex(0); // 先设置为0，后续会重新排序

                // 设置城市代码和联系方式
                pnrCt.setCityCode("SHA"); // 默认使用上海
                pnrCt.setCtText("T-" + ctText);

                // 构建输入值
                String inputValue = StrUtil.format("SHA/T-{}", ctText);
                pnrCt.setInputValue(inputValue);

                // 添加到列表中
                pnrCtList.add(pnrCt);
            }
        }

        // 处理电话信息列表
        if (CollUtil.isNotEmpty(contactInfo.getPhoneInfoList())) {
            for (BookPnrDto.PhoneInfo phoneInfo : contactInfo.getPhoneInfoList()) {
                MnjxPnrOsi pnrOsi = new MnjxPnrOsi();
                pnrOsi.setPnrOsiId(IdUtil.getSnowflake(1, 1).nextIdStr());
                pnrOsi.setPnrId(pnr.getPnrId());
                pnrOsi.setPnrIndex(0); // 先设置为0，后续会重新排序
                pnrOsi.setAirlineCode(phoneInfo.getAirline());
                pnrOsi.setPnrOsiType("CTCT");

                // 构建输入值和信息
                String osiInfo = StrUtil.format("OSI {} CTCT{}", phoneInfo.getAirline(), phoneInfo.getPhoneNumber());
                pnrOsi.setPnrOsiInfo(osiInfo);
                pnrOsi.setInputValue(osiInfo);

                // 添加到列表中
                pnrOsiList.add(pnrOsi);
            }
        }

        if (StrUtil.isNotEmpty(contactInfo.getEmail())) {
            MnjxPnrOsi pnrOsi = new MnjxPnrOsi();
            pnrOsi.setPnrOsiId(IdUtil.getSnowflake(1, 1).nextIdStr());
            pnrOsi.setPnrId(pnr.getPnrId());
            pnrOsi.setPnrIndex(0); // 先设置为0，后续会重新排序
            pnrOsi.setAirlineCode(airlineCode);
            pnrOsi.setPnrOsiType("CTCE");

            // 构建输入值和信息
            String osiInfo = StrUtil.format("OSI {} CTCE{}", airlineCode, contactInfo.getEmail());
            pnrOsi.setPnrOsiInfo(osiInfo);
            pnrOsi.setInputValue(osiInfo);

            // 添加到列表中
            pnrOsiList.add(pnrOsi);
        }
    }

    /**
     * 处理备注信息
     *
     * @param remarks    备注信息
     * @param pnr        PNR实体
     * @param pnrRmkList 备注列表
     * @param pnrOsiList OSI列表
     */
    private void processRemarks(List<BookPnrDto.Remark> remarks, MnjxPnr pnr, List<MnjxPnrRmk> pnrRmkList, List<MnjxNmRmk> nmRmkList, List<MnjxPnrOsi> pnrOsiList,
                                List<MnjxPnrSeg> pnrSegList, List<MnjxPnrNm> pnrNmList, List<MnjxNmOsi> nmOsiList) throws SguiResultException {
        for (BookPnrDto.Remark remark : remarks) {
            if ("RMK".equals(remark.getType())) {
                if (remark.getText().replaceAll("/P\\d+", "").equals(remark.getText())) {
                    // 创建备注信息
                    MnjxPnrRmk pnrRmk = new MnjxPnrRmk();
                    pnrRmk.setPnrRmkId(IdUtil.getSnowflake(1, 1).nextIdStr());
                    pnrRmk.setPnrId(pnr.getPnrId());
                    pnrRmk.setPnrIndex(0); // 先设置为0，后续会重新排序
                    pnrRmk.setRmkName("RMK");

                    // 构建输入值
                    String inputValue = StrUtil.format("RMK {}", remark.getText());
                    pnrRmk.setRmkInfo(inputValue);
                    pnrRmk.setInputValue(inputValue);

                    // 添加到列表中
                    pnrRmkList.add(pnrRmk);
                } else {
                    String[] parts = remark.getText().split("/P");
                    if (parts.length > 1) {
                        Optional<MnjxPnrNm> optional = pnrNmList.stream()
                                .filter(p -> p.getPsgIndex().equals(Integer.parseInt(parts[1])))
                                .findFirst();
                        if (!optional.isPresent()) {
                            throw new SguiResultException("PSGR ID");
                        }
                        MnjxPnrNm pnrNm = optional.get();
                        MnjxNmRmk nmRmk = new MnjxNmRmk();
                        nmRmk.setNmRmkId(IdUtil.getSnowflake(1, 1).nextIdStr());
                        nmRmk.setPnrNmId(pnrNm.getPnrNmId());
                        nmRmk.setPnrIndex(0); // 先设置为0，后续会重新排序

                        // 构建输入值
                        String inputValue = StrUtil.format("RMK {}", remark.getText());
                        nmRmk.setRmkInfo(inputValue);
                        nmRmk.setInputValue(inputValue);

                        // 添加到列表中
                        nmRmkList.add(nmRmk);
                    }
                }
            } else if ("OSI".equals(remark.getType())) {
                if (StrUtil.isNotEmpty(remark.getAirlineCode()) && pnrSegList.stream().noneMatch(p -> p.getFlightNo().substring(0, 2).equals(remark.getAirlineCode()))) {
                    throw new SguiResultException("AIRLINE");
                }
                if (remark.getText().replaceAll("/P\\d+", "").equals(remark.getText())) {
                    // PNR级别的OSI
                    // 创建OSI信息
                    MnjxPnrOsi pnrOsi = new MnjxPnrOsi();
                    pnrOsi.setPnrOsiId(IdUtil.getSnowflake(1, 1).nextIdStr());
                    pnrOsi.setPnrId(pnr.getPnrId());
                    pnrOsi.setPnrIndex(0); // 先设置为0，后续会重新排序
                    pnrOsi.setAirlineCode(remark.getAirlineCode());

                    // 构建输入值和信息
                    String osiInfo;
                    if (StrUtil.isNotEmpty(remark.getAirlineCode())) {
                        osiInfo = StrUtil.format("OSI {} {}", remark.getAirlineCode(), remark.getText());
                    } else {
                        osiInfo = StrUtil.format("OSI {}", remark.getText());
                    }
                    pnrOsi.setPnrOsiInfo(osiInfo);
                    pnrOsi.setInputValue(osiInfo);

                    // 添加到列表中
                    pnrOsiList.add(pnrOsi);
                } else {
                    // 旅客级别的OSI
                    String[] parts = remark.getText().split("/P");
                    if (parts.length > 1) {
                        Optional<MnjxPnrNm> optional = pnrNmList.stream()
                                .filter(p -> p.getPsgIndex().equals(Integer.parseInt(parts[1])))
                                .findFirst();
                        if (!optional.isPresent()) {
                            throw new SguiResultException("PSGR ID");
                        }
                        MnjxPnrNm pnrNm = optional.get();
                        MnjxNmOsi nmOsi = new MnjxNmOsi();
                        nmOsi.setPnrOsiId(IdUtil.getSnowflake(1, 1).nextIdStr());
                        nmOsi.setPnrNmId(pnrNm.getPnrNmId());
                        nmOsi.setPnrIndex(0); // 先设置为0，后续会重新排序
                        nmOsi.setAirlineCode(remark.getAirlineCode());
                        // 构建输入值和信息
                        String osiInfo;
                        if (StrUtil.isNotEmpty(remark.getAirlineCode())) {
                            osiInfo = StrUtil.format("OSI {} {}", remark.getAirlineCode(), remark.getText());
                        } else {
                            osiInfo = StrUtil.format("OSI {}", remark.getText());
                        }
                        nmOsi.setPnrOsiInfo(osiInfo);
                        nmOsi.setInputValue(osiInfo);
                        nmOsiList.add(nmOsi);
                    }
                }
            }
        }
    }

    /**
     * 处理特殊服务
     *
     * @param specialServices   特殊服务
     * @param pnrNmList         旅客列表
     * @param passengerIndexMap 旅客ID与PNR序号的映射
     * @param nmSsrList         特服列表
     */
    private void processSpecialServices(List<BookPnrDto.SpecialService> specialServices, List<MnjxPnrNm> pnrNmList,
                                        Map<String, Integer> passengerIndexMap, List<MnjxNmSsr> nmSsrList, List<MnjxPnrSeg> pnrSegList) {

        for (BookPnrDto.SpecialService service : specialServices) {
            if (CollUtil.isEmpty(service.getPassengerIds()) || CollUtil.isEmpty(service.getSpecialServiceSegments())) {
                continue;
            }

            for (String passengerId : service.getPassengerIds()) {
                if (!passengerIndexMap.containsKey(passengerId)) {
                    continue;
                }

                Integer passengerPnrIndex = passengerIndexMap.get(passengerId);
                MnjxPnrNm pnrNm = null;

                // 从列表中查找旅客
                for (MnjxPnrNm nm : pnrNmList) {
                    if (nm.getPnrIndex().equals(passengerPnrIndex)) {
                        pnrNm = nm;
                        break;
                    }
                }

                if (pnrNm == null) {
                    continue;
                }

                for (BookPnrDto.SpecialServiceSegment segment : service.getSpecialServiceSegments()) {
                    MnjxNmSsr nmSsr = new MnjxNmSsr();
                    nmSsr.setNmSsrId(IdUtil.getSnowflake(1, 1).nextIdStr());
                    nmSsr.setPnrNmId(pnrNm.getPnrNmId());
                    nmSsr.setPnrIndex(0); // 先设置为0，后续会重新排序
                    nmSsr.setSsrType(service.getType());
                    nmSsr.setActionCode("HK");
                    nmSsr.setAirlineCode(segment.getMarketingAirline());
                    nmSsr.setOrgDst(segment.getDepartureAirport() + segment.getArrivalAirport());
                    Integer pnrSegNo = pnrSegList.stream()
                            .filter(p -> StrUtil.isNotEmpty(p.getFlightNo()) && p.getFlightNo().equals(segment.getMarketingAirline() + segment.getMarketingFlightNumber()))
                            .collect(Collectors.toList())
                            .get(0)
                            .getPnrSegNo();
                    nmSsr.setPnrSegNo(pnrSegNo);
                    nmSsr.setFltDate(segment.getDepartureDate());

                    // 解析日期
                    String departureDate = segment.getDepartureDate();
                    String dayOfMonth = departureDate.substring(8, 10);
                    String month = departureDate.substring(5, 7);
                    String monthAbbr = this.getMonthAbbr(month);

                    // 根据特服类型构建不同的输入值
                    String ssrInfo;
                    if (service.getType().endsWith("ML") && !"SPML".equals(service.getType())) {
                        // 其他餐食格式：SSR MOML CA NN1 SHATFU 8541 I14MAY/P1
                        ssrInfo = StrUtil.format("SSR {} {} {}1 {}{} {} {}{}{}/P{}",
                                service.getType(), segment.getMarketingAirline(), nmSsr.getActionCode(),
                                segment.getDepartureAirport(), segment.getArrivalAirport(),
                                segment.getMarketingFlightNumber(), segment.getCabinCode(), dayOfMonth, monthAbbr,
                                pnrNm.getPsgIndex());
                    } else {
                        // 其他类型格式：SSR 对应的type CA NN1 SHATFU 8541 Y14MAY text/P1
                        ssrInfo = StrUtil.format("SSR {} {} {}1 {}{} {} {}{}{}{}/P{}",
                                service.getType(), segment.getMarketingAirline(), nmSsr.getActionCode(),
                                segment.getDepartureAirport(), segment.getArrivalAirport(),
                                segment.getMarketingFlightNumber(), segment.getCabinCode(), dayOfMonth, monthAbbr,
                                StrUtil.isNotEmpty(service.getText()) ? " " + service.getText() : "",
                                pnrNm.getPsgIndex());
                    }

                    nmSsr.setSsrInfo(ssrInfo);
                    nmSsr.setInputValue(ssrInfo);

                    // 添加到列表中
                    nmSsrList.add(nmSsr);
                }
            }
        }
    }

    /**
     * 处理价格信息
     *
     * @param prices            价格信息
     * @param pnr               PNR实体
     * @param pnrNmList         旅客列表
     * @param passengerIndexMap 旅客ID与PNR序号的映射
     * @param nmFpList          旅客FP列表
     * @param nmFnList          旅客FN列表
     * @param nmFcList          旅客FC列表
     * @param pnrFpList         PNR级别FP列表
     * @param pnrFnList         PNR级别FN列表
     * @param pnrFcList         PNR级别FC列表
     *
     * @return 运价是否能成功写入
     */
    private boolean processPriceInfo(List<BookPnrDto.Price> prices, MnjxPnr pnr, List<MnjxPnrNm> pnrNmList, Map<String, Integer> passengerIndexMap,
                                  List<MnjxNmFp> nmFpList, List<MnjxNmFn> nmFnList, List<MnjxNmFc> nmFcList,
                                  List<MnjxPnrFp> pnrFpList, List<MnjxPnrFn> pnrFnList, List<MnjxPnrFc> pnrFcList, List<MnjxNmXn> xnList, List<MnjxNmRmk> nmRmkList) {
        String fcPriceCabinReg = "(\\d+[.]0{2})([A-Z]).*";
        for (BookPnrDto.Price price : prices) {
            if (CollUtil.isEmpty(price.getPriceItems())) {
                continue;
            }

            // 判断有没有婴儿运价
            boolean hasInfantPrice = false;
            // 是否有运价挂在旅客上
            boolean hasPassengerIdPrice = false;
            // 婴儿运价是否挂在旅客上
            boolean infWithPassengerId = false;
            for (BookPnrDto.PriceItem priceItem : price.getPriceItems()) {
                if (hasInfantPrice && hasPassengerIdPrice) {
                    infWithPassengerId = true;
                    break;
                }
                if ("INF".equals(priceItem.getPassengerTypeUpdate())) {
                    hasInfantPrice = true;
                } else if (CollUtil.isNotEmpty(priceItem.getPassengerIds())) {
                    hasPassengerIdPrice = true;
                }
            }

            for (BookPnrDto.PriceItem priceItem : price.getPriceItems()) {
                // 判断是否是婴儿价格
                boolean isInfant = "INF".equals(priceItem.getPassengerTypeUpdate());
                String patType = this.getPatType(priceItem, isInfant);

                // 如果非婴儿的passengerIds为空，说明该运价是加在pnr上的
                if (CollUtil.isEmpty(priceItem.getPassengerIds()) && !isInfant) {
                    // 如果是军警残运价，判断所有旅客是否都是军警残
                    if ("GM".equals(priceItem.getPassengerTypeUpdate()) || "JC".equals(priceItem.getPassengerTypeUpdate())) {
                        for (MnjxPnrNm pnrNm : pnrNmList) {
                            if (nmRmkList.stream().noneMatch(r -> r.getPnrNmId().equals(pnrNm.getPnrNmId()) && "GMJC".equals(r.getRmkName()))) {
                                return false;
                            }
                        }
                    }

                    // 处理PNR级别的价格信息
                    // 处理FP信息
                    if (StrUtil.isNotEmpty(price.getPayMethod())) {
                        MnjxPnrFp pnrFp = new MnjxPnrFp();
                        pnrFp.setPnrFpId(IdUtil.getSnowflake(1, 1).nextIdStr());
                        pnrFp.setPnrId(pnr.getPnrId());
                        pnrFp.setPnrIndex(0); // 先设置为0，后续会重新排序
                        pnrFp.setIsBaby(0);
                        pnrFp.setPayType(price.getPayMethod());
                        pnrFp.setPatType(patType);
                        pnrFp.setCurrencyType("CNY");

                        // 构建输入值
                        String inputValue = StrUtil.format("FP/{},CNY", price.getPayMethod());
                        pnrFp.setInputValue(inputValue);

                        // 添加到列表中
                        pnrFpList.add(pnrFp);
                    }

                    // 处理FN信息
                    if (StrUtil.isNotEmpty(priceItem.getFn())) {
                        MnjxPnrFn pnrFn = new MnjxPnrFn();
                        pnrFn.setPnrFnId(IdUtil.getSnowflake(1, 1).nextIdStr());
                        pnrFn.setPnrId(pnr.getPnrId());
                        pnrFn.setPnrIndex(0); // 先设置为0，后续会重新排序
                        pnrFn.setIsBaby(0);

                        // 设置货币和价格
                        pnrFn.setFCurrency("CNY");
                        pnrFn.setSCurrency("CNY");
                        pnrFn.setACurrency("CNY");

                        // 设置手续费
                        pnrFn.setCRate(BigDecimal.valueOf(priceItem.getCommissionRate()));

                        if (StrUtil.isNotEmpty(priceItem.getTicketAmount())) {
                            pnrFn.setFPrice(new BigDecimal(priceItem.getTicketAmount()));
                            pnrFn.setSPrice(new BigDecimal(priceItem.getTicketAmount()));
                        }

                        if (StrUtil.isNotEmpty(priceItem.getTotalAmount())) {
                            pnrFn.setAPrice(new BigDecimal(priceItem.getTotalAmount()));
                        }

                        pnrFn.setTCnPrice(new BigDecimal(priceItem.getFund()));
                        pnrFn.setTYqPrice(new BigDecimal(priceItem.getFuel()));
                        pnrFn.setXPrice(pnrFn.getTCnPrice().add(pnrFn.getTYqPrice()));

                        pnrFn.setPatType(patType);

                        // 构建输入值
                        String inputValue = StrUtil.format("{}/ACNY{}", priceItem.getFn(), priceItem.getTotalAmount());
                        inputValue = inputValue.substring(0, 2) + "/A" + inputValue.substring(2);
                        pnrFn.setInputValue(inputValue);

                        // 添加到列表中
                        pnrFnList.add(pnrFn);
                    }

                    // 处理FC信息
                    if (StrUtil.isNotEmpty(priceItem.getFc()) && CollUtil.isNotEmpty(priceItem.getSegmentInfos())) {
                        MnjxPnrFc pnrFc = new MnjxPnrFc();
                        pnrFc.setPnrFcId(IdUtil.getSnowflake(1, 1).nextIdStr());
                        pnrFc.setPnrId(pnr.getPnrId());
                        pnrFc.setPnrIndex(0); // 先设置为0，后续会重新排序
                        pnrFc.setIsBaby(0);
                        pnrFc.setCurrency("CNY");
                        pnrFc.setPatType(patType);

                        if (StrUtil.isNotEmpty(priceItem.getTicketAmount())) {
                            pnrFc.setTotalPrice(new BigDecimal(priceItem.getTicketAmount()));
                        }

                        // 构建输入值
                        String inputValue = priceItem.getFc();
                        // 解析输入值，FC/PEK A-19MAY26 CA SHA 2150.00Y // SZX A-19MAY26 CA PEK 3650.00Y CNY5800.00END
                        String[] split = inputValue.split(" ");
                        int segIndex = 1;
                        for (String s : split) {
                            if (ReUtil.isMatch(fcPriceCabinReg, s)) {
                                List<String> allGroups = ReUtil.getAllGroups(Pattern.compile(fcPriceCabinReg), s);
                                BigDecimal cabinPrice = new BigDecimal(allGroups.get(1));
                                String segCabin = allGroups.get(2);
                                switch (segIndex) {
                                    case 1:
                                        pnrFc.setSeg1Price(cabinPrice);
                                        pnrFc.setSeg1Cabin(segCabin);
                                        pnrFc.setSeg1PriceType(patType);
                                        break;
                                    case 2:
                                        pnrFc.setSeg2Price(cabinPrice);
                                        pnrFc.setSeg2Cabin(segCabin);
                                        pnrFc.setSeg2PriceType(patType);
                                        break;
                                    case 3:
                                        pnrFc.setSeg3Price(cabinPrice);
                                        pnrFc.setSeg3Cabin(segCabin);
                                        pnrFc.setSeg3PriceType(patType);
                                        break;
                                    case 4:
                                        pnrFc.setSeg4Price(cabinPrice);
                                        pnrFc.setSeg4Cabin(segCabin);
                                        pnrFc.setSeg4PriceType(patType);
                                        break;
                                    case 5:
                                        pnrFc.setSeg5Price(cabinPrice);
                                        pnrFc.setSeg5Cabin(segCabin);
                                        pnrFc.setSeg5PriceType(patType);
                                        break;
                                    default:
                                        break;
                                }
                                segIndex++;
                            } else if ("//".equals(s)) {
                                segIndex++;
                            }
                        }

                        inputValue = inputValue.substring(0, 2) + "/A" + inputValue.substring(2);
                        pnrFc.setInputValue(inputValue);

                        // 添加到列表中
                        pnrFcList.add(pnrFc);
                    }
                } else if (isInfant) {
                    // 婴儿运价中passengerIds始终是空的，需要通过其他旅客的passengerIds判断需不需要指定到P旅客序号上
                    if (infWithPassengerId) {
                        for (MnjxNmXn nmXn : xnList) {
                            String psgIndex = nmXn.getInputValue().substring(nmXn.getInputValue().lastIndexOf("P") + 1);
                            // 处理FP信息
                            if (StrUtil.isNotEmpty(price.getPayMethod())) {
                                MnjxNmFp nmFp = new MnjxNmFp();
                                nmFp.setNmFpId(IdUtil.getSnowflake(1, 1).nextIdStr());
                                nmFp.setPnrNmId(nmXn.getPnrNmId());
                                nmFp.setPnrIndex(0); // 先设置为0，后续会重新排序
                                nmFp.setIsBaby(1);
                                nmFp.setPayType(price.getPayMethod());
                                nmFp.setPatType(patType);
                                nmFp.setCurrencyType("CNY");

                                // 构建输入值
                                // 成人和儿童：FP/CASH,CNY/P1
                                // 婴儿：FP/IN/CASH,CNY/P1
                                String inputValue = StrUtil.format("FP/IN/{},CNY/P{}", price.getPayMethod(), psgIndex);
                                nmFp.setInputValue(inputValue);

                                // 添加到列表中
                                nmFpList.add(nmFp);
                            }

                            // 处理FN信息
                            if (StrUtil.isNotEmpty(priceItem.getFn())) {
                                MnjxNmFn nmFn = new MnjxNmFn();
                                nmFn.setNmFnId(IdUtil.getSnowflake(1, 1).nextIdStr());
                                nmFn.setPnrNmId(nmXn.getPnrNmId());
                                nmFn.setPnrIndex(0); // 先设置为0，后续会重新排序
                                nmFn.setIsBaby(1);
                                nmFn.setPatType(patType);

                                // 解析FN信息
                                String fn = priceItem.getFn();

                                // 设置货币和价格
                                nmFn.setFCurrency("CNY");
                                nmFn.setSCurrency("CNY");
                                nmFn.setACurrency("CNY");
                                nmFn.setXCurrency("CNY");
                                nmFn.setTCnCurrency("CNY");
                                nmFn.setTYqCurrency("CNY");

                                if (StrUtil.isNotEmpty(priceItem.getTicketAmount())) {
                                    nmFn.setFPrice(new BigDecimal(priceItem.getTicketAmount()));
                                    nmFn.setSPrice(new BigDecimal(priceItem.getTicketAmount()));
                                }

                                if (StrUtil.isNotEmpty(priceItem.getTotalAmount())) {
                                    nmFn.setAPrice(new BigDecimal(priceItem.getTotalAmount()));
                                }

                                nmFn.setCRate(new BigDecimal(priceItem.getCommissionRate()));
                                nmFn.setTCnPrice(new BigDecimal(priceItem.getFund()));
                                nmFn.setTYqPrice(new BigDecimal(priceItem.getFuel()));
                                nmFn.setXPrice(nmFn.getTCnPrice().add(nmFn.getTYqPrice()));

                                // 构建输入值
                                // 婴儿：FN/IN/A/FCNY100.00/SCNY100.00/C0.00/TEXEMPTCN/TEXEMPTYQ/ACNY100.00/P1
                                String inputValue = StrUtil.format("{}/ACNY{}/P{}", fn, priceItem.getTotalAmount(), psgIndex);
                                inputValue = inputValue.substring(0, 5) + "/A" + inputValue.substring(5);
                                nmFn.setInputValue(inputValue);

                                // 添加到列表中
                                nmFnList.add(nmFn);
                            }

                            // 处理FC信息
                            if (StrUtil.isNotEmpty(priceItem.getFc()) && CollUtil.isNotEmpty(priceItem.getSegmentInfos())) {
                                MnjxNmFc nmFc = new MnjxNmFc();
                                nmFc.setNmFcId(IdUtil.getSnowflake(1, 1).nextIdStr());
                                nmFc.setPnrNmId(nmXn.getPnrNmId());
                                nmFc.setPnrIndex(0); // 先设置为0，后续会重新排序
                                nmFc.setIsBaby(1);
                                nmFc.setCurrency("CNY");
                                nmFc.setPatType(patType);

                                if (StrUtil.isNotEmpty(priceItem.getTicketAmount())) {
                                    nmFc.setTotalPrice(new BigDecimal(priceItem.getTicketAmount()));
                                }

                                // 构建输入值
                                String inputValue = priceItem.getFc();
                                // 解析输入值，FC/IN/PEK A-19MAY26 CA SHA 220.00YIN90 // SZX A-19MAY26 CA PEK 370.00YIN90 CNY590.00END/TEXT/*(IN)/P1
                                String[] split = inputValue.split(" ");
                                int segIndex = 1;
                                for (String s : split) {
                                    if (ReUtil.isMatch(fcPriceCabinReg, s)) {
                                        List<String> allGroups = ReUtil.getAllGroups(Pattern.compile(fcPriceCabinReg), s);
                                        BigDecimal cabinPrice = new BigDecimal(allGroups.get(1));
                                        String segCabin = allGroups.get(2);
                                        switch (segIndex) {
                                            case 1:
                                                nmFc.setSeg1Price(cabinPrice);
                                                nmFc.setSeg1Cabin(segCabin);
                                                nmFc.setSeg1PriceType("IN");
                                                break;
                                            case 2:
                                                nmFc.setSeg2Price(cabinPrice);
                                                nmFc.setSeg2Cabin(segCabin);
                                                nmFc.setSeg2PriceType("IN");
                                                break;
                                            case 3:
                                                nmFc.setSeg3Price(cabinPrice);
                                                nmFc.setSeg3Cabin(segCabin);
                                                nmFc.setSeg3PriceType("IN");
                                                break;
                                            case 4:
                                                nmFc.setSeg4Price(cabinPrice);
                                                nmFc.setSeg4Cabin(segCabin);
                                                nmFc.setSeg4PriceType("IN");
                                                break;
                                            case 5:
                                                nmFc.setSeg5Price(cabinPrice);
                                                nmFc.setSeg5Cabin(segCabin);
                                                nmFc.setSeg5PriceType("IN");
                                                break;
                                            default:
                                                break;
                                        }
                                        segIndex++;
                                    } else if ("//".equals(s)) {
                                        segIndex++;
                                    }
                                }

                                // 根据旅客类型添加后缀
                                inputValue += " **(IN)";

                                // 添加旅客序号
                                inputValue += "/P" + psgIndex;
                                inputValue = inputValue.substring(0, 2) + "/A" + inputValue.substring(2);

                                nmFc.setInputValue(inputValue);

                                // 添加到列表中
                                nmFcList.add(nmFc);
                            }
                        }
                    } else {
                        // 婴儿价格信息也加到PNR上
                        // 处理FP信息
                        if (StrUtil.isNotEmpty(price.getPayMethod())) {
                            MnjxPnrFp pnrFp = new MnjxPnrFp();
                            pnrFp.setPnrFpId(IdUtil.getSnowflake(1, 1).nextIdStr());
                            pnrFp.setPnrId(pnr.getPnrId());
                            pnrFp.setPnrIndex(0); // 先设置为0，后续会重新排序
                            pnrFp.setIsBaby(1); // 设置为婴儿
                            pnrFp.setPayType(price.getPayMethod());
                            pnrFp.setPatType(patType);
                            pnrFp.setCurrencyType("CNY");

                            // 构建输入值
                            String inputValue = StrUtil.format("FP/IN/{},CNY", price.getPayMethod());
                            pnrFp.setInputValue(inputValue);

                            // 添加到列表中
                            pnrFpList.add(pnrFp);
                        }

                        // 处理FN信息
                        if (StrUtil.isNotEmpty(priceItem.getFn())) {
                            MnjxPnrFn pnrFn = new MnjxPnrFn();
                            pnrFn.setPnrFnId(IdUtil.getSnowflake(1, 1).nextIdStr());
                            pnrFn.setPnrId(pnr.getPnrId());
                            pnrFn.setPnrIndex(0); // 先设置为0，后续会重新排序
                            pnrFn.setIsBaby(1); // 设置为婴儿
                            pnrFn.setPatType(patType);

                            // 设置货币和价格
                            pnrFn.setFCurrency("CNY");
                            pnrFn.setSCurrency("CNY");
                            pnrFn.setACurrency("CNY");
                            pnrFn.setXCurrency("CNY");
                            pnrFn.setTCnCurrency("CNY");
                            pnrFn.setTYqCurrency("CNY");

                            if (StrUtil.isNotEmpty(priceItem.getTicketAmount())) {
                                pnrFn.setFPrice(new BigDecimal(priceItem.getTicketAmount()));
                                pnrFn.setSPrice(new BigDecimal(priceItem.getTicketAmount()));
                            }

                            if (StrUtil.isNotEmpty(priceItem.getTotalAmount())) {
                                pnrFn.setAPrice(new BigDecimal(priceItem.getTotalAmount()));
                            }

                            pnrFn.setCRate(new BigDecimal(priceItem.getCommissionRate()));
                            pnrFn.setTCnPrice(new BigDecimal(priceItem.getFund()));
                            pnrFn.setTYqPrice(new BigDecimal(priceItem.getFuel()));
                            pnrFn.setXPrice(pnrFn.getTCnPrice().add(pnrFn.getTYqPrice()));

                            // 构建输入值
                            String inputValue = StrUtil.format("{}/ACNY{}",
                                    priceItem.getFn(), priceItem.getTotalAmount());
                            inputValue = inputValue.substring(0, 5) + "/A" + inputValue.substring(5);
                            pnrFn.setInputValue(inputValue);

                            // 添加到列表中
                            pnrFnList.add(pnrFn);
                        }

                        // 处理FC信息
                        if (StrUtil.isNotEmpty(priceItem.getFc()) && CollUtil.isNotEmpty(priceItem.getSegmentInfos())) {
                            MnjxPnrFc pnrFc = new MnjxPnrFc();
                            pnrFc.setPnrFcId(IdUtil.getSnowflake(1, 1).nextIdStr());
                            pnrFc.setPnrId(pnr.getPnrId());
                            pnrFc.setPnrIndex(0); // 先设置为0，后续会重新排序
                            pnrFc.setIsBaby(1); // 设置为婴儿
                            pnrFc.setPatType(patType);
                            pnrFc.setCurrency("CNY");

                            if (StrUtil.isNotEmpty(priceItem.getTicketAmount())) {
                                pnrFc.setTotalPrice(new BigDecimal(priceItem.getTicketAmount()));
                            }

                            // 构建输入值
                            String inputValue = priceItem.getFc().contains(" **(IN)") ? priceItem.getFc() : priceItem.getFc() + " **(IN)";
                            // 解析输入值，FC/IN/PEK A-19MAY26 CA SHA 220.00YIN90 // SZX A-19MAY26 CA PEK 370.00YIN90 CNY590.00END/TEXT/*(IN)
                            String[] split = inputValue.split(" ");
                            int segIndex = 1;
                            for (String s : split) {
                                if (ReUtil.isMatch(fcPriceCabinReg, s)) {
                                    List<String> allGroups = ReUtil.getAllGroups(Pattern.compile(fcPriceCabinReg), s);
                                    BigDecimal cabinPrice = new BigDecimal(allGroups.get(1));
                                    String segCabin = allGroups.get(2);
                                    switch (segIndex) {
                                        case 1:
                                            pnrFc.setSeg1Price(cabinPrice);
                                            pnrFc.setSeg1Cabin(segCabin);
                                            pnrFc.setSeg1PriceType("IN");
                                            break;
                                        case 2:
                                            pnrFc.setSeg2Price(cabinPrice);
                                            pnrFc.setSeg2Cabin(segCabin);
                                            pnrFc.setSeg2PriceType("IN");
                                            break;
                                        case 3:
                                            pnrFc.setSeg3Price(cabinPrice);
                                            pnrFc.setSeg3Cabin(segCabin);
                                            pnrFc.setSeg3PriceType("IN");
                                            break;
                                        case 4:
                                            pnrFc.setSeg4Price(cabinPrice);
                                            pnrFc.setSeg4Cabin(segCabin);
                                            pnrFc.setSeg4PriceType("IN");
                                            break;
                                        case 5:
                                            pnrFc.setSeg5Price(cabinPrice);
                                            pnrFc.setSeg5Cabin(segCabin);
                                            pnrFc.setSeg5PriceType("IN");
                                            break;
                                        default:
                                            break;
                                    }
                                    segIndex++;
                                } else if ("//".equals(s)) {
                                    segIndex++;
                                }
                            }

                            inputValue = inputValue.substring(0, 5) + "/A" + inputValue.substring(5);
                            pnrFc.setInputValue(inputValue);

                            // 添加到列表中
                            pnrFcList.add(pnrFc);
                        }
                    }
                } else {
                    // 处理旅客的价格信息，这里只会处理非婴儿的场景，婴儿的场景在上面infWithPassengerId中处理
                    if (CollUtil.isNotEmpty(priceItem.getPassengerIds())) {
                        for (String passengerId : priceItem.getPassengerIds()) {
                            if (!passengerIndexMap.containsKey(passengerId)) {
                                continue;
                            }

                            Integer passengerPnrIndex = passengerIndexMap.get(passengerId);
                            MnjxPnrNm pnrNm = pnrNmList.stream()
                                    .filter(nm -> nm.getPnrIndex().equals(passengerPnrIndex))
                                    .findFirst()
                                    .orElse(null);

                            // 从列表中查找旅客

                            if (pnrNm == null) {
                                continue;
                            }

                            // 如果是军警残运价，判断所有旅客是否都是军警残
                            if ("GM".equals(priceItem.getPassengerTypeUpdate()) || "JC".equals(priceItem.getPassengerTypeUpdate())) {
                                if (nmRmkList.stream().noneMatch(r -> r.getPnrNmId().equals(pnrNm.getPnrNmId()) && "GMJC".equals(r.getRmkName()))) {
                                    return false;
                                }
                            }

                            // 处理FP信息
                            if (StrUtil.isNotEmpty(price.getPayMethod())) {
                                MnjxNmFp nmFp = new MnjxNmFp();
                                nmFp.setNmFpId(IdUtil.getSnowflake(1, 1).nextIdStr());
                                nmFp.setPnrNmId(pnrNm.getPnrNmId());
                                nmFp.setPnrIndex(0); // 先设置为0，后续会重新排序
                                nmFp.setIsBaby(0);
                                nmFp.setPayType(price.getPayMethod());
                                nmFp.setPatType(patType);
                                nmFp.setCurrencyType("CNY");

                                // 构建输入值
                                // 成人和儿童：FP/CASH,CNY/P1
                                // 婴儿：FP/IN/CASH,CNY/P1
                                String inputValue = StrUtil.format("FP/{},CNY/P{}", price.getPayMethod(), pnrNm.getPsgIndex());
                                nmFp.setInputValue(inputValue);

                                // 添加到列表中
                                nmFpList.add(nmFp);
                            }

                            // 处理FN信息
                            if (StrUtil.isNotEmpty(priceItem.getFn())) {
                                MnjxNmFn nmFn = new MnjxNmFn();
                                nmFn.setNmFnId(IdUtil.getSnowflake(1, 1).nextIdStr());
                                nmFn.setPnrNmId(pnrNm.getPnrNmId());
                                nmFn.setPnrIndex(0); // 先设置为0，后续会重新排序
                                nmFn.setIsBaby(0);
                                nmFn.setPatType(patType);

                                // 解析FN信息
                                String fn = priceItem.getFn();

                                // 设置货币和价格
                                nmFn.setFCurrency("CNY");
                                nmFn.setSCurrency("CNY");
                                nmFn.setACurrency("CNY");
                                nmFn.setXCurrency("CNY");
                                nmFn.setTCnCurrency("CNY");
                                nmFn.setTYqCurrency("CNY");

                                if (StrUtil.isNotEmpty(priceItem.getTicketAmount())) {
                                    nmFn.setFPrice(new BigDecimal(priceItem.getTicketAmount()));
                                    nmFn.setSPrice(new BigDecimal(priceItem.getTicketAmount()));
                                }

                                if (StrUtil.isNotEmpty(priceItem.getTotalAmount())) {
                                    nmFn.setAPrice(new BigDecimal(priceItem.getTotalAmount()));
                                }

                                nmFn.setCRate(new BigDecimal(priceItem.getCommissionRate()));
                                nmFn.setTCnPrice(new BigDecimal(priceItem.getFund()));
                                nmFn.setTYqPrice(new BigDecimal(priceItem.getFuel()));
                                nmFn.setXPrice(nmFn.getTCnPrice().add(nmFn.getTYqPrice()));

                                // 构建输入值
                                String inputValue;
                                // 成人：FN/A/FCNY800.00/SCNY800.00/C0.00/XCNY190.00/TCNY50.00CN/TCNY140.00YQ/ACNY990.00/P1
                                // 儿童：FN/A/FCNY470.00/SCNY470.00/C0.00/XCNY70.00/TEXEMPTCN/TCNY70.00YQ/ACNY540.00/P2
                                inputValue = StrUtil.format("{}/ACNY{}/P{}", fn, priceItem.getTotalAmount(), pnrNm.getPsgIndex());
                                inputValue = inputValue.substring(0, 2) + "/A" + inputValue.substring(2);
                                nmFn.setInputValue(inputValue);

                                // 添加到列表中
                                nmFnList.add(nmFn);
                            }

                            // 处理FC信息
                            if (StrUtil.isNotEmpty(priceItem.getFc()) && CollUtil.isNotEmpty(priceItem.getSegmentInfos())) {
                                MnjxNmFc nmFc = new MnjxNmFc();
                                nmFc.setNmFcId(IdUtil.getSnowflake(1, 1).nextIdStr());
                                nmFc.setPnrNmId(pnrNm.getPnrNmId());
                                nmFc.setPnrIndex(0); // 先设置为0，后续会重新排序
                                nmFc.setIsBaby(0);
                                nmFc.setCurrency("CNY");
                                nmFc.setPatType(patType);

                                if (StrUtil.isNotEmpty(priceItem.getTicketAmount())) {
                                    nmFc.setTotalPrice(new BigDecimal(priceItem.getTicketAmount()));
                                }

                                // 构建输入值
                                String inputValue = priceItem.getFc();
                                // 解析输入值，FC/IN/PEK A-19MAY26 CA SHA 220.00YIN90 // SZX A-19MAY26 CA PEK 370.00YIN90 CNY590.00END/TEXT/*(IN)/P1
                                String[] split = inputValue.split(" ");
                                int segIndex = 1;
                                for (String s : split) {
                                    if (ReUtil.isMatch(fcPriceCabinReg, s)) {
                                        List<String> allGroups = ReUtil.getAllGroups(Pattern.compile(fcPriceCabinReg), s);
                                        BigDecimal cabinPrice = new BigDecimal(allGroups.get(1));
                                        String segCabin = allGroups.get(2);
                                        switch (segIndex) {
                                            case 1:
                                                nmFc.setSeg1Price(cabinPrice);
                                                nmFc.setSeg1Cabin(segCabin);
                                                nmFc.setSeg1PriceType(patType);
                                                break;
                                            case 2:
                                                nmFc.setSeg2Price(cabinPrice);
                                                nmFc.setSeg2Cabin(segCabin);
                                                nmFc.setSeg2PriceType(patType);
                                                break;
                                            case 3:
                                                nmFc.setSeg3Price(cabinPrice);
                                                nmFc.setSeg3Cabin(segCabin);
                                                nmFc.setSeg3PriceType(patType);
                                                break;
                                            case 4:
                                                nmFc.setSeg4Price(cabinPrice);
                                                nmFc.setSeg4Cabin(segCabin);
                                                nmFc.setSeg4PriceType(patType);
                                                break;
                                            case 5:
                                                nmFc.setSeg5Price(cabinPrice);
                                                nmFc.setSeg5Cabin(segCabin);
                                                nmFc.setSeg5PriceType(patType);
                                                break;
                                            default:
                                                break;
                                        }
                                        segIndex++;
                                    } else if ("//".equals(s)) {
                                        segIndex++;
                                    }
                                }

                                // 根据旅客类型添加后缀
                                switch (patType) {
                                    case "CH":
                                        inputValue = inputValue.contains(" **(CH)") ? inputValue : inputValue + " **(CH)";
                                        break;
                                    case "GM":
                                        inputValue = inputValue.contains(" **(GM)") ? inputValue : inputValue + " **(GM)";
                                        break;
                                    case "JC":
                                        inputValue = inputValue.contains(" **(JC)") ? inputValue : inputValue + " **(JC)";
                                        break;
                                }

                                // 添加旅客序号
                                inputValue += "/P" + pnrNm.getPsgIndex();
                                inputValue = inputValue.substring(0, 2) + "/A" + inputValue.substring(2);

                                nmFc.setInputValue(inputValue);

                                // 添加到列表中
                                nmFcList.add(nmFc);
                            }
                        }
                    }
                }
            }
        }
        return true;
    }

    /**
     * 获取运价旅客类型
     *
     * @param priceItem
     * @param isInfant
     * @return
     */
    private String getPatType(BookPnrDto.PriceItem priceItem, boolean isInfant) {
        String patType;
        if (isInfant) {
            patType = "IN";
        } else {
            if ("CHD".equals(priceItem.getPassengerTypeUpdate()) || "UM".equals(priceItem.getPassengerTypeUpdate())) {
                patType = "CH";
                if (priceItem.getChdUsingAdtPrice()) {
                    patType = "AD";
                }
            } else if ("GM".equals(priceItem.getPassengerTypeUpdate())) {
                patType = "GM";
            } else if ("JC".equals(priceItem.getPassengerTypeUpdate())) {
                patType = "JC";
            } else {
                patType = "AD";
            }
        }
        return patType;
    }

    /**
     * 处理出票时限
     *
     * @param timeLimit 出票时限
     * @param pnr       PNR实体
     * @param pnrTkList 出票时限列表
     */
    private void processTimeLimit(BookPnrDto.TimeLimit timeLimit, MnjxPnr pnr, List<MnjxPnrTk> pnrTkList, BookPnrDto.Segment segment) throws SguiResultException {
        if (timeLimit != null && StrUtil.isNotEmpty(timeLimit.getTimeLimit())) {
            MnjxPnrTk pnrTk = new MnjxPnrTk();
            pnrTk.setPnrTkId(IdUtil.getSnowflake(1, 1).nextIdStr());
            pnrTk.setPnrId(pnr.getPnrId());
            pnrTk.setPnrIndex(0); // 先设置为0，后续会重新排序
            pnrTk.setPnrTkType("TL");

            // 解析时间限制 2025-05-22 05:30
            String timeLimitStr = timeLimit.getTimeLimit();
            Date timeLimitDate = DateUtil.parse(timeLimitStr);
            String timePart = DateUtil.format(timeLimitDate, "HHmm");
            String datePart = DateUtil.format(timeLimitDate, "yyyy-MM-dd");

            if (timeLimitDate.compareTo(DateUtil.parse(segment.getDepartureDate() + " " + segment.getDepartureTime() + ":00")) >= 0) {
                throw new SguiResultException("请检查出票时限，出票时限不能在航班起飞后");
            }

            pnrTk.setPlanEtdzDate(datePart);
            pnrTk.setPlanEtdzTime(timePart);

            // 构建输入值
            // 格式："TL/1330/14MAY25/SHA001"
            String officeNo = iSguiCommonService.getCurrentUserInfo().getMnjxOffice().getOfficeNo();
            if (!officeNo.equals(timeLimit.getOffice())) {
                throw new SguiResultException("NON-CRT OFFICE");
            }
            pnrTk.setEtdzOffice(officeNo);
            String inputValue = StrUtil.format("TL/{}/{}/{}", timePart, DateUtils.ymd2Com(datePart), officeNo);
            pnrTk.setInputValue(inputValue);

            // 添加到列表中
            pnrTkList.add(pnrTk);
        }
    }

    /**
     * 重新排序所有项的pnr_index
     * 按照以下顺序排序：
     * pnr nm组（nm组内部排序根据psgIndex排，比如有4个旅客，nm组的pnr_index排序需要按照psgIndex从小到达排1 2 3 4）
     * pnr seg组
     * pnr ct组
     * nm ct组
     * pnr tk组
     * pnr fc组
     * nm fc组
     * nm ssr组
     * pnr osi组
     * nm osi组
     * pnr rmk组
     * nm rmk组
     * pnr tc组
     * pnr fn组
     * nm fn组
     * pnr ei组
     * nm ei组
     * nm oi组
     * pnr tn组
     * nm xn组
     * pnr fp组
     * nm fp组
     *
     * @param pnr           PNR实体
     * @param pnrNmList     旅客列表
     * @param pnrSegList    航段列表
     * @param pnrCtList     PNR联系方式列表
     * @param nmCtList      旅客联系方式列表
     * @param pnrTkList     出票时限列表
     * @param pnrFcList     PNR级别FC列表
     * @param nmFcList      旅客级别FC列表
     * @param nmSsrList     旅客特服列表
     * @param pnrOsiList    PNR级别OSI列表
     * @param nmOsiList     旅客级别OSI列表
     * @param pnrRmkList    PNR级别备注列表
     * @param nmRmkList     旅客级别备注列表
     * @param pnrTcList     PNR级别TC列表
     * @param pnrFnList     PNR级别FN列表
     * @param nmFnList      旅客级别FN列表
     * @param pnrEiList     PNR级别EI列表
     * @param nmEiList      旅客级别EI列表
     * @param nmOiList      旅客级别OI列表
     * @param nmTnList      PNR级别TN列表
     * @param nmXnList      婴儿列表
     * @param pnrFpList     PNR级别FP列表
     * @param nmFpList      旅客级别FP列表
     * @param pnrRecordList 历史记录列表
     */
    @Override
    public void reorderPnrIndexes(MnjxPnr pnr, List<MnjxPnrNm> pnrNmList, List<MnjxPnrSeg> pnrSegList,
                                  List<MnjxPnrCt> pnrCtList, List<MnjxNmCt> nmCtList, List<MnjxPnrTk> pnrTkList,
                                  List<MnjxPnrFc> pnrFcList, List<MnjxNmFc> nmFcList, List<MnjxNmSsr> nmSsrList,
                                  List<MnjxPnrOsi> pnrOsiList, List<MnjxNmOsi> nmOsiList, List<MnjxPnrRmk> pnrRmkList,
                                  List<MnjxNmRmk> nmRmkList, List<MnjxPnrTc> pnrTcList, List<MnjxNmTc> nmTcList, List<MnjxPnrFn> pnrFnList,
                                  List<MnjxNmFn> nmFnList, List<MnjxPnrEi> pnrEiList, List<MnjxNmEi> nmEiList,
                                  List<MnjxNmOi> nmOiList, List<MnjxPnrNmTn> nmTnList, List<MnjxNmXn> nmXnList,
                                  List<MnjxPnrFp> pnrFpList, List<MnjxNmFp> nmFpList, List<MnjxPnrRecord> pnrRecordList) {
        int newIndex = 1;

        // 1. pnr nm组（按psgIndex排序）
        if (CollUtil.isNotEmpty(pnrNmList)) {
            // 按psgIndex排序
            pnrNmList.sort(Comparator.comparing(MnjxPnrNm::getPsgIndex));
            for (MnjxPnrNm pnrNm : pnrNmList) {
                // 可能进行了删除旅客的操作，需要重排psgIndex
                pnrNm.setPsgIndex(newIndex);
                pnrRecordList.add(this.constructRecord(pnr.getPnrId(), newIndex, pnrNm.getInputValue(), "NM"));
                pnrNm.setPnrIndex(newIndex++);
            }
        }

        // 2. pnr seg组
        List<MnjxPnrSeg> sortedSegList = new ArrayList<>();
        if (CollUtil.isNotEmpty(pnrSegList)) {
            // 判断航段有没有修改
            if (pnrSegList.stream().noneMatch(p -> p.getPnrSegNo() == 0)) {
                // 没有修改，直接用原来的航段组
                sortedSegList = pnrSegList;
            } else {
                // 航段有进行修改，先提取非SA的航段组，按起飞日期时间排序
                sortedSegList = pnrSegList.stream()
                        .filter(p -> !"SA".equals(p.getPnrSegType()))
                        .sorted(Comparator.comparing(p -> DateUtil.parseDateTime(StrUtil.format("{} {}:{}:00", p.getFlightDate(), p.getEstimateOff().substring(0, 2), p.getEstimateOff().substring(2)))))
                        .collect(Collectors.toList());
                // 然后按航站匹配插入SA的航段（如果有）
                if (pnrSegList.stream().anyMatch(p -> "SA".equals(p.getPnrSegType()))) {
                    // 筛选SA航段组
                    List<MnjxPnrSeg> saSegList = pnrSegList.stream()
                            .filter(p -> "SA".equals(p.getPnrSegType()))
                            .collect(Collectors.toList());
                    // 遍历SA航段组去匹配已提取的航段组sortedSegList
                    // 如果当前SA航段只有出发航站和当前已提取的航段的到达航站相同，则当前SA航段插入到当前已提取航段后面
                    // 如果当前SA航段只有到达航站和当前已提取的航段的出发航站相同，则当前SA航段插入到当前已提取航段前面
                    // 如果都无法匹配，判断该SA航段的pnrSegNo是否为0，不为0则插入到pnrSegNo-1的下标位置，为0则插入到最后
                    for (MnjxPnrSeg saSeg : saSegList) {
                        int insertIndex = 0;
                        for (int i = 0; i < sortedSegList.size(); i++) {
                            MnjxPnrSeg sortedSeg = sortedSegList.get(i);
                            // SA的出发匹配到达
                            if (saSeg.getOrg().equals(sortedSeg.getDst())) {
                                insertIndex = i + 1;
                            }
                            // SA的到达匹配出发
                            else if (saSeg.getDst().equals(sortedSeg.getOrg())) {
                                insertIndex = i;
                            }
                            // 都不能匹配
                            else {
                                // 是原来存在的，则按pnrSegNo-1插入
                                if (saSeg.getPnrSegNo() > 0) {
                                    insertIndex = saSeg.getPnrSegNo() - 1;
                                }
                                // 否则放最后
                                else {
                                    insertIndex = sortedSegList.size();
                                }
                            }
                        }
                        sortedSegList.add(insertIndex, saSeg);
                    }
                }
            }
            AtomicInteger pnrSegNo = new AtomicInteger(1);
            sortedSegList.forEach(s -> s.setPnrSegNo(pnrSegNo.getAndIncrement()));

            for (MnjxPnrSeg pnrSeg : sortedSegList) {
                MnjxPnrRecord pnrRecord = this.constructRecord(pnr.getPnrId(), newIndex, pnrSeg.getInputValue(), "SEG");
                pnrRecordList.add(pnrRecord);
                pnrSeg.setPnrIndex(newIndex++);
            }
        }

        // 3. pnr ct组
        if (CollUtil.isNotEmpty(pnrCtList)) {
            for (MnjxPnrCt pnrCt : pnrCtList) {
                pnrRecordList.add(this.constructRecord(pnr.getPnrId(), newIndex, pnrCt.getInputValue(), "CT"));
                pnrCt.setPnrIndex(newIndex++);
            }
        }

        // 4. nm ct组
        if (CollUtil.isNotEmpty(nmCtList)) {
            for (MnjxNmCt nmCt : nmCtList) {
                // 如果是删除了旅客，P序号可能会变动
                Integer psgIndex = pnrNmList.stream()
                        .filter(nm -> nmCt.getPnrNmId().equals(nm.getPnrNmId()))
                        .collect(Collectors.toList())
                        .get(0)
                        .getPsgIndex();
                nmCt.setInputValue(nmCt.getInputValue().replaceAll("/P\\d+\\b$", CharSequenceUtil.format("/P{}", psgIndex)));
                pnrRecordList.add(this.constructRecord(pnr.getPnrId(), newIndex, nmCt.getInputValue(), "NM CT"));
                nmCt.setPnrIndex(newIndex++);
            }
        }

        // 5. pnr tk组
        if (CollUtil.isNotEmpty(pnrTkList)) {
            for (MnjxPnrTk pnrTk : pnrTkList) {
                pnrRecordList.add(this.constructRecord(pnr.getPnrId(), newIndex, pnrTk.getInputValue(), "TK"));
                pnrTk.setPnrIndex(newIndex++);
            }
        }

        // 6. pnr fc组
        if (CollUtil.isNotEmpty(pnrFcList)) {
            for (MnjxPnrFc pnrFc : pnrFcList) {
                pnrRecordList.add(this.constructRecord(pnr.getPnrId(), newIndex, pnrFc.getInputValue(), "FC"));
                pnrFc.setPnrIndex(newIndex++);
            }
        }

        // 7. nm fc组
        if (CollUtil.isNotEmpty(nmFcList)) {
            for (MnjxNmFc nmFc : nmFcList) {
                // 如果是删除了旅客，P序号可能会变动
                Integer psgIndex = pnrNmList.stream()
                        .filter(nm -> nmFc.getPnrNmId().equals(nm.getPnrNmId()))
                        .collect(Collectors.toList())
                        .get(0)
                        .getPsgIndex();
                nmFc.setInputValue(nmFc.getInputValue().replaceAll("/P\\d+\\b$", CharSequenceUtil.format("/P{}", psgIndex)));
                pnrRecordList.add(this.constructRecord(pnr.getPnrId(), newIndex, nmFc.getInputValue(), "NM FC"));
                nmFc.setPnrIndex(newIndex++);
            }
        }

        // 8. nm ssr组
        if (CollUtil.isNotEmpty(nmSsrList)) {
            for (MnjxNmSsr nmSsr : nmSsrList) {
                // 如果是删除了旅客，P序号可能会变动
                Integer psgIndex = pnrNmList.stream()
                        .filter(nm -> nmSsr.getPnrNmId().equals(nm.getPnrNmId()))
                        .collect(Collectors.toList())
                        .get(0)
                        .getPsgIndex();
                nmSsr.setInputValue(nmSsr.getInputValue().replaceAll("/P\\d+\\b$", CharSequenceUtil.format("/P{}", psgIndex)));
                nmSsr.setSsrInfo(nmSsr.getInputValue().replaceAll("/P\\d+\\b$", CharSequenceUtil.format("/P{}", psgIndex)));
                // 新增或修改航段的时候产生的带航段序号的SSR，需要更新最新的排序序号
                if (nmSsr.getPnrSegNo() != null && nmSsr.getPnrSegNo() == 0) {
                    nmSsr.setPnrSegNo(sortedSegList.stream()
                            .filter(s -> !"SA".equals(s.getPnrSegType()) && s.getOrg().equals(nmSsr.getOrgDst().substring(0, 3)) && s.getDst().equals(nmSsr.getOrgDst().substring(3)))
                            .collect(Collectors.toList())
                            .get(0)
                            .getPnrSegNo());
                }
                pnrRecordList.add(this.constructRecord(pnr.getPnrId(), newIndex, nmSsr.getInputValue(), "SSR"));
                nmSsr.setPnrIndex(newIndex++);
            }
        }

        // 9. pnr osi组
        if (CollUtil.isNotEmpty(pnrOsiList)) {
            for (MnjxPnrOsi pnrOsi : pnrOsiList) {
                pnrRecordList.add(this.constructRecord(pnr.getPnrId(), newIndex, pnrOsi.getInputValue(), "OSI"));
                pnrOsi.setPnrIndex(newIndex++);
            }
        }

        // 10. nm osi组
        if (CollUtil.isNotEmpty(nmOsiList)) {
            for (MnjxNmOsi nmOsi : nmOsiList) {
                // 如果是删除了旅客，P序号可能会变动
                Integer psgIndex = pnrNmList.stream()
                        .filter(nm -> nmOsi.getPnrNmId().equals(nm.getPnrNmId()))
                        .collect(Collectors.toList())
                        .get(0)
                        .getPsgIndex();
                nmOsi.setInputValue(nmOsi.getInputValue().replaceAll("/P\\d+\\b$", CharSequenceUtil.format("/P{}", psgIndex)));
                nmOsi.setPnrOsiInfo(nmOsi.getInputValue().replaceAll("/P\\d+\\b$", CharSequenceUtil.format("/P{}", psgIndex)));
                pnrRecordList.add(this.constructRecord(pnr.getPnrId(), newIndex, nmOsi.getInputValue(), "NM OSI"));
                nmOsi.setPnrIndex(newIndex++);
            }
        }

        // 11. pnr rmk组
        if (CollUtil.isNotEmpty(pnrRmkList)) {
            for (MnjxPnrRmk pnrRmk : pnrRmkList) {
                pnrRecordList.add(this.constructRecord(pnr.getPnrId(), newIndex, pnrRmk.getInputValue(), "RMK"));
                pnrRmk.setPnrIndex(newIndex++);
            }
        }

        // 12. nm rmk组
        if (CollUtil.isNotEmpty(nmRmkList)) {
            for (MnjxNmRmk nmRmk : nmRmkList) {
                // 如果是删除了旅客，P序号可能会变动
                Integer psgIndex = pnrNmList.stream()
                        .filter(nm -> nmRmk.getPnrNmId().equals(nm.getPnrNmId()))
                        .collect(Collectors.toList())
                        .get(0)
                        .getPsgIndex();
                nmRmk.setInputValue(nmRmk.getInputValue().replaceAll("/P\\d+\\b$", CharSequenceUtil.format("/P{}", psgIndex)));
                nmRmk.setRmkInfo(nmRmk.getInputValue().replaceAll("/P\\d+\\b$", CharSequenceUtil.format("/P{}", psgIndex)));
                pnrRecordList.add(this.constructRecord(pnr.getPnrId(), newIndex, nmRmk.getInputValue(), "NM RMK"));
                nmRmk.setPnrIndex(newIndex++);
            }
        }

        // 13. pnr tc组
        if (CollUtil.isNotEmpty(pnrTcList)) {
            for (MnjxPnrTc pnrTc : pnrTcList) {
                pnrRecordList.add(this.constructRecord(pnr.getPnrId(), newIndex, pnrTc.getInputValue(), "TC"));
                pnrTc.setPnrIndex(newIndex++);
            }
        }

        // 13. nm tc组
        if (CollUtil.isNotEmpty(nmTcList)) {
            for (MnjxNmTc nmTc : nmTcList) {
                // 如果是删除了旅客，P序号可能会变动
                Integer psgIndex = pnrNmList.stream()
                        .filter(nm -> nmTc.getPnrNmId().equals(nm.getPnrNmId()))
                        .collect(Collectors.toList())
                        .get(0)
                        .getPsgIndex();
                nmTc.setInputValue(nmTc.getInputValue().replaceAll("/P\\d+\\b$", CharSequenceUtil.format("/P{}", psgIndex)));
                nmTc.setTcInfo(nmTc.getInputValue().replaceAll("/P\\d+\\b$", CharSequenceUtil.format("/P{}", psgIndex)));
                pnrRecordList.add(this.constructRecord(pnr.getPnrId(), newIndex, nmTc.getInputValue(), "NM TC"));
                nmTc.setPnrIndex(newIndex++);
            }
        }

        // 14. pnr fn组
        if (CollUtil.isNotEmpty(pnrFnList)) {
            for (MnjxPnrFn pnrFn : pnrFnList) {
                pnrRecordList.add(this.constructRecord(pnr.getPnrId(), newIndex, pnrFn.getInputValue(), "FN"));
                pnrFn.setPnrIndex(newIndex++);
            }
        }

        // 15. nm fn组
        if (CollUtil.isNotEmpty(nmFnList)) {
            for (MnjxNmFn nmFn : nmFnList) {
                // 如果是删除了旅客，P序号可能会变动
                Integer psgIndex = pnrNmList.stream()
                        .filter(nm -> nmFn.getPnrNmId().equals(nm.getPnrNmId()))
                        .collect(Collectors.toList())
                        .get(0)
                        .getPsgIndex();
                nmFn.setInputValue(nmFn.getInputValue().replaceAll("/P\\d+\\b$", CharSequenceUtil.format("/P{}", psgIndex)));
                pnrRecordList.add(this.constructRecord(pnr.getPnrId(), newIndex, nmFn.getInputValue(), "NM FN"));
                nmFn.setPnrIndex(newIndex++);
            }
        }

        // 18. nm oi组
        if (CollUtil.isNotEmpty(nmOiList)) {
            for (MnjxNmOi nmOi : nmOiList) {
                // 如果是删除了旅客，P序号可能会变动
                Integer psgIndex = pnrNmList.stream()
                        .filter(nm -> nmOi.getPnrNmId().equals(nm.getPnrNmId()))
                        .collect(Collectors.toList())
                        .get(0)
                        .getPsgIndex();
                nmOi.setOiInfo(nmOi.getOiInfo().replaceAll("/P\\d+\\b$", CharSequenceUtil.format("/P{}", psgIndex)));
                pnrRecordList.add(this.constructRecord(pnr.getPnrId(), newIndex, nmOi.getOiInfo(), "OI"));
                nmOi.setPnrIndex(newIndex++);
            }
        }

        // 19. pnr tn组
        if (CollUtil.isNotEmpty(nmTnList)) {
            for (MnjxPnrNmTn pnrTn : nmTnList) {
                Integer psgIndex = null;
                if (StrUtil.isNotEmpty(pnrTn.getPnrNmId())) {
                    // 如果是删除了旅客，P序号可能会变动
                    psgIndex = pnrNmList.stream()
                            .filter(nm -> pnrTn.getPnrNmId().equals(nm.getPnrNmId()))
                            .collect(Collectors.toList())
                            .get(0)
                            .getPsgIndex();
                } else if (StrUtil.isNotEmpty(pnrTn.getNmXnId())) {
                    String pnrNmId = nmXnList.stream()
                            .filter(x -> x.getNmXnId().equals(pnrTn.getNmXnId()))
                            .collect(Collectors.toList())
                            .get(0)
                            .getPnrNmId();
                    psgIndex = pnrNmList.stream()
                            .filter(nm -> pnrNmId.equals(nm.getPnrNmId()))
                            .collect(Collectors.toList())
                            .get(0)
                            .getPsgIndex();
                }
                pnrTn.setInputValue(pnrTn.getInputValue().replaceAll("/P\\d+\\b$", CharSequenceUtil.format("/P{}", psgIndex)));
                MnjxPnrRecord tnRecord = this.constructRecord(pnr.getPnrId(), newIndex, pnrTn.getInputValue(), "TN");
                // TN的record需要额外记录该票的所属人，用于改签后查原票找到对应的旅客
                tnRecord.setPnrNmId(StrUtil.isNotEmpty(pnrTn.getPnrNmId()) ? pnrTn.getPnrNmId() : pnrTn.getNmXnId());
                tnRecord.setIssuedTime(pnrTn.getIssuedTime());
                tnRecord.setPrinterId(pnrTn.getPrinterId());
                pnrRecordList.add(tnRecord);
                pnrTn.setPnrIndex(newIndex++);
            }
        }

        // 16. pnr ei组
        if (CollUtil.isNotEmpty(pnrEiList)) {
            for (MnjxPnrEi pnrEi : pnrEiList) {
                pnrRecordList.add(this.constructRecord(pnr.getPnrId(), newIndex, pnrEi.getInputValue(), "EI"));
                pnrEi.setPnrIndex(newIndex++);
            }
        }

        // 17. nm ei组
        if (CollUtil.isNotEmpty(nmEiList)) {
            for (MnjxNmEi nmEi : nmEiList) {
                // 如果是删除了旅客，P序号可能会变动
                Integer psgIndex = pnrNmList.stream()
                        .filter(nm -> nmEi.getPnrNmId().equals(nm.getPnrNmId()))
                        .collect(Collectors.toList())
                        .get(0)
                        .getPsgIndex();
                nmEi.setInputValue(nmEi.getInputValue().replaceAll("/P\\d+\\b$", CharSequenceUtil.format("/P{}", psgIndex)));
                nmEi.setEiInfo(nmEi.getInputValue().replaceAll("/P\\d+\\b$", CharSequenceUtil.format("/P{}", psgIndex)));
                pnrRecordList.add(this.constructRecord(pnr.getPnrId(), newIndex, nmEi.getInputValue(), "NM EI"));
                nmEi.setPnrIndex(newIndex++);
            }
        }

        // 21. pnr fp组
        if (CollUtil.isNotEmpty(pnrFpList)) {
            for (MnjxPnrFp pnrFp : pnrFpList) {
                pnrRecordList.add(this.constructRecord(pnr.getPnrId(), newIndex, pnrFp.getInputValue(), "FP"));
                pnrFp.setPnrIndex(newIndex++);
            }
        }

        // 22. nm fp组
        if (CollUtil.isNotEmpty(nmFpList)) {
            for (MnjxNmFp nmFp : nmFpList) {
                // 如果是删除了旅客，P序号可能会变动
                Integer psgIndex = pnrNmList.stream()
                        .filter(nm -> nmFp.getPnrNmId().equals(nm.getPnrNmId()))
                        .collect(Collectors.toList())
                        .get(0)
                        .getPsgIndex();
                nmFp.setInputValue(nmFp.getInputValue().replaceAll("/P\\d+\\b$", CharSequenceUtil.format("/P{}", psgIndex)));
                pnrRecordList.add(this.constructRecord(pnr.getPnrId(), newIndex, nmFp.getInputValue(), "NM FP"));
                nmFp.setPnrIndex(newIndex++);
            }
        }

        // 20. nm xn组
        if (CollUtil.isNotEmpty(nmXnList)) {
            for (MnjxNmXn nmXn : nmXnList) {
                // 如果是删除了旅客，P序号可能会变动
                Integer psgIndex = pnrNmList.stream()
                        .filter(nm -> nmXn.getPnrNmId().equals(nm.getPnrNmId()))
                        .collect(Collectors.toList())
                        .get(0)
                        .getPsgIndex();
                nmXn.setInputValue(nmXn.getInputValue().replaceAll("/P\\d+\\b$", CharSequenceUtil.format("/P{}", psgIndex)));
                pnrRecordList.add(this.constructRecord(pnr.getPnrId(), newIndex, nmXn.getInputValue(), "XN"));
                nmXn.setPnrIndex(newIndex++);
            }
        }

        // 更新PNR最大索引值
        pnr.setMaxIndex(newIndex - 1);
    }

    /**
     * Title: constructRecord
     * Description: <br>
     *
     * @param pnrId
     * @param pnrIndex
     * @param inputValue
     * @param type
     * @return {@link MnjxPnrRecord}
     * <AUTHOR>
     * @date 2025/5/20 16:18
     */
    private MnjxPnrRecord constructRecord(String pnrId, Integer pnrIndex, String inputValue, String type) {
        // 构建历史记录
        MnjxPnrRecord pnrRecord = new MnjxPnrRecord();
        pnrRecord.setPnrRecordId(IdUtil.getSnowflake(1, 1).nextIdStr());
        pnrRecord.setPnrId(pnrId);
        pnrRecord.setPnrIndex(pnrIndex);
        pnrRecord.setAtNo("001"); // 第一次封口
        pnrRecord.setPnrType(type); // 旅客级别价格信息
        pnrRecord.setInputValue(inputValue);
        return pnrRecord;
    }
}
