package com.swcares.service.bkc.impl;

import cn.hutool.core.date.DateUtil;
import com.swcares.core.exception.SguiResultException;
import com.swcares.core.security.custom.UserInfo;
import com.swcares.entity.MnjxPnr;
import com.swcares.entity.MnjxPnrSub;
import com.swcares.obj.dto.SubQueueCrsPnrDto;
import com.swcares.obj.dto.SubQueueCrsPnrReqDto;
import com.swcares.obj.vo.SubQueueCrsPnrResVo;
import com.swcares.obj.vo.SubQueueCrsPnrVo;
import com.swcares.service.IMnjxPnrService;
import com.swcares.service.IMnjxPnrSubService;
import com.swcares.service.ISguiCommonService;
import com.swcares.service.bkc.IQueueService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 队列服务实现类
 *
 * <AUTHOR>
 * @date 2025/08/04 17:30
 */
@Slf4j
@Service
public class QueueServiceImpl implements IQueueService {

    @Resource
    private IMnjxPnrSubService iMnjxPnrSubService;

    @Resource
    private IMnjxPnrService iMnjxPnrService;

    @Resource
    private ISguiCommonService iSguiCommonService;

    @Override
    public SubQueueCrsPnrVo queryQueueList(SubQueueCrsPnrDto dto) throws SguiResultException {
        try {
            // 获取当前登录用户的工作号ID
            UserInfo userInfo = iSguiCommonService.getCurrentUserInfo();

            // 1. 根据当前登录工作号siId查询mnjx_pnr_sub表，获取关注的PNR ID列表
            List<MnjxPnrSub> pnrSubList = iMnjxPnrSubService.lambdaQuery()
                    .eq(MnjxPnrSub::getSiId, userInfo.getSiId())
                    .list();
            
            SubQueueCrsPnrVo vo = new SubQueueCrsPnrVo();
            vo.setAgent(userInfo.getSiNo());
            vo.setErrorInfo(null);
            vo.setSynchTime(DateUtil.format(DateUtil.date(), "yyyy-MM-dd HH:mm:ss"));
            
            if (pnrSubList.isEmpty()) {
                vo.setSubscriptionsInfos(new ArrayList<>());
                return vo;
            }
            
            // 提取PNR ID列表
            List<String> pnrIdList = pnrSubList.stream()
                    .map(MnjxPnrSub::getPnrId)
                    .collect(Collectors.toList());
            
            // 2. 根据PNR ID列表查询mnjx_pnr表，获取PNR信息
            List<MnjxPnr> pnrList = iMnjxPnrService.lambdaQuery()
                    .in(MnjxPnr::getPnrId, pnrIdList)
                    .list();
            
            // 3. 构建subscriptionsInfos
            List<SubQueueCrsPnrVo.SubscriptionsInfo> subscriptionsInfos = new ArrayList<>();
            for (MnjxPnr pnr : pnrList) {
                SubQueueCrsPnrVo.SubscriptionsInfo subscriptionInfo = new SubQueueCrsPnrVo.SubscriptionsInfo();
                subscriptionInfo.setPnr(pnr.getPnrCrs());
                // subscriptionsInfos的time值为pnr的create_time
                subscriptionInfo.setTime(DateUtil.format(pnr.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
                
                // 4. readStatusInfos按response数据构建
                List<SubQueueCrsPnrVo.ReadStatusInfo> readStatusInfos = this.buildReadStatusInfos();
                subscriptionInfo.setReadStatusInfos(readStatusInfos);
                
                subscriptionsInfos.add(subscriptionInfo);
            }
            
            vo.setSubscriptionsInfos(subscriptionsInfos);
            return vo;
            
        } catch (Exception e) {
            log.error("查询关注PNR列表失败", e);
            throw new SguiResultException("查询关注PNR列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 构建读取状态信息列表
     * 根据response.json中的数据结构构建
     */
    private List<SubQueueCrsPnrVo.ReadStatusInfo> buildReadStatusInfos() {
        List<String> reasonList = Arrays.asList("GQ", "RP", "KK", "RE", "SR", "TC", "TL", "SC");
        List<SubQueueCrsPnrVo.ReadStatusInfo> readStatusInfos = new ArrayList<>();
        
        for (String reason : reasonList) {
            SubQueueCrsPnrVo.ReadStatusInfo readStatusInfo = new SubQueueCrsPnrVo.ReadStatusInfo();
            readStatusInfo.setStatusCode("0");
            readStatusInfo.setReason(reason);
            readStatusInfos.add(readStatusInfo);
        }
        
        return readStatusInfos;
    }

    @Override
    public SubQueueCrsPnrResVo subQueueCrsPnr(SubQueueCrsPnrReqDto dto) throws SguiResultException {
        try {
            // 获取当前登录用户的工作号ID
            String siId = iSguiCommonService.getCurrentUserInfo().getSiId();

            // 1. 根据请求PNR编号列表查询mnjx_pnr表的pnr_crs字段值，如果不存在，报错：PNR不存在
            for (String pnrNo : dto.getPnrNos()) {
                MnjxPnr pnr = iMnjxPnrService.lambdaQuery()
                        .eq(MnjxPnr::getPnrCrs, pnrNo)
                        .one();

                if (pnr == null) {
                    throw new SguiResultException("PNR不存在: " + pnrNo);
                }

                // 2. 获取当前登录的工作号id和查询的pnr id，存储到mnjx_pnr_sub表
                // 检查是否已经关注过该PNR
                MnjxPnrSub existingSub = iMnjxPnrSubService.lambdaQuery()
                        .eq(MnjxPnrSub::getSiId, siId)
                        .eq(MnjxPnrSub::getPnrId, pnr.getPnrId())
                        .one();

                if (existingSub == null) {
                    // 如果没有关注过，则新增关注记录
                    MnjxPnrSub pnrSub = new MnjxPnrSub();
                    pnrSub.setSiId(siId);
                    pnrSub.setPnrId(pnr.getPnrId());
                    iMnjxPnrSubService.save(pnrSub);
                }
            }

            SubQueueCrsPnrResVo vo = new SubQueueCrsPnrResVo();
            vo.setSuccess(true);
            vo.setPnrDetailDtos(null);
            return vo;

        } catch (SguiResultException e) {
            throw e;
        } catch (Exception e) {
            log.error("关注PNR失败", e);
            throw new SguiResultException("关注PNR失败: " + e.getMessage());
        }
    }
}
