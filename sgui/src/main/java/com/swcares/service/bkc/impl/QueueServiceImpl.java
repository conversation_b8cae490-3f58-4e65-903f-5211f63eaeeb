package com.swcares.service.bkc.impl;

import cn.hutool.core.date.DateUtil;
import com.swcares.core.exception.SguiResultException;
import com.swcares.core.security.custom.UserInfo;
import com.swcares.entity.*;
import com.swcares.obj.dto.SubQueueCrsPnrDetailDto;
import com.swcares.obj.dto.SubQueueCrsPnrDto;
import com.swcares.obj.dto.SubQueueCrsPnrReqDto;
import com.swcares.obj.dto.UnsubscribeCrsQueueDto;
import com.swcares.obj.vo.SubQueueCrsPnrDetailVo;
import com.swcares.obj.vo.SubQueueCrsPnrResVo;
import com.swcares.obj.vo.SubQueueCrsPnrVo;
import com.swcares.service.*;
import com.swcares.service.bkc.IQueueService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 队列服务实现类
 *
 * <AUTHOR>
 * @date 2025/08/04 17:30
 */
@Slf4j
@Service
public class QueueServiceImpl implements IQueueService {

    @Resource
    private IMnjxPnrSubService iMnjxPnrSubService;

    @Resource
    private IMnjxPnrService iMnjxPnrService;

    @Resource
    private ISguiCommonService iSguiCommonService;

    @Resource
    private IMnjxPnrNmService iMnjxPnrNmService;

    @Resource
    private IMnjxPnrSegService iMnjxPnrSegService;

    @Resource
    private IMnjxNmXnService iMnjxNmXnService;

    @Resource
    private IMnjxPnrNmTnService iMnjxPnrNmTnService;

    @Override
    public SubQueueCrsPnrVo queryQueueList(SubQueueCrsPnrDto dto) throws SguiResultException {
        try {
            // 获取当前登录用户的工作号ID
            UserInfo userInfo = iSguiCommonService.getCurrentUserInfo();

            // 1. 根据当前登录工作号siId查询mnjx_pnr_sub表，获取关注的PNR ID列表
            List<MnjxPnrSub> pnrSubList = iMnjxPnrSubService.lambdaQuery()
                    .eq(MnjxPnrSub::getSiId, userInfo.getSiId())
                    .list();
            
            SubQueueCrsPnrVo vo = new SubQueueCrsPnrVo();
            vo.setAgent(userInfo.getSiNo());
            vo.setErrorInfo(null);
            vo.setSynchTime(DateUtil.format(DateUtil.date(), "yyyy-MM-dd HH:mm:ss"));
            
            if (pnrSubList.isEmpty()) {
                vo.setSubscriptionsInfos(new ArrayList<>());
                return vo;
            }
            
            // 提取PNR ID列表
            List<String> pnrIdList = pnrSubList.stream()
                    .map(MnjxPnrSub::getPnrId)
                    .collect(Collectors.toList());
            
            // 2. 根据PNR ID列表查询mnjx_pnr表，获取PNR信息
            List<MnjxPnr> pnrList = iMnjxPnrService.lambdaQuery()
                    .in(MnjxPnr::getPnrId, pnrIdList)
                    .list();
            
            // 3. 构建subscriptionsInfos
            List<SubQueueCrsPnrVo.SubscriptionsInfo> subscriptionsInfos = new ArrayList<>();
            for (MnjxPnr pnr : pnrList) {
                SubQueueCrsPnrVo.SubscriptionsInfo subscriptionInfo = new SubQueueCrsPnrVo.SubscriptionsInfo();
                subscriptionInfo.setPnr(pnr.getPnrCrs());
                // subscriptionsInfos的time值为pnr的create_time
                subscriptionInfo.setTime(DateUtil.format(pnr.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
                
                // 4. readStatusInfos按response数据构建
                List<SubQueueCrsPnrVo.ReadStatusInfo> readStatusInfos = this.buildReadStatusInfos();
                subscriptionInfo.setReadStatusInfos(readStatusInfos);
                
                subscriptionsInfos.add(subscriptionInfo);
            }
            
            vo.setSubscriptionsInfos(subscriptionsInfos);
            return vo;
            
        } catch (Exception e) {
            log.error("查询关注PNR列表失败", e);
            throw new SguiResultException("查询关注PNR列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 构建读取状态信息列表
     * 根据response.json中的数据结构构建
     */
    private List<SubQueueCrsPnrVo.ReadStatusInfo> buildReadStatusInfos() {
        List<String> reasonList = Arrays.asList("GQ", "RP", "KK", "RE", "SR", "TC", "TL", "SC");
        List<SubQueueCrsPnrVo.ReadStatusInfo> readStatusInfos = new ArrayList<>();
        
        for (String reason : reasonList) {
            SubQueueCrsPnrVo.ReadStatusInfo readStatusInfo = new SubQueueCrsPnrVo.ReadStatusInfo();
            readStatusInfo.setStatusCode("0");
            readStatusInfo.setReason(reason);
            readStatusInfos.add(readStatusInfo);
        }
        
        return readStatusInfos;
    }

    @Override
    public SubQueueCrsPnrResVo subQueueCrsPnr(SubQueueCrsPnrReqDto dto) throws SguiResultException {
        try {
            // 获取当前登录用户的工作号ID
            String siId = iSguiCommonService.getCurrentUserInfo().getSiId();

            // 1. 根据请求PNR编号列表查询mnjx_pnr表的pnr_crs字段值，如果不存在，报错：PNR不存在
            for (String pnrNo : dto.getPnrNos()) {
                MnjxPnr pnr = iMnjxPnrService.lambdaQuery()
                        .eq(MnjxPnr::getPnrCrs, pnrNo)
                        .one();

                if (pnr == null) {
                    throw new SguiResultException("PNR不存在: " + pnrNo);
                }

                // 2. 获取当前登录的工作号id和查询的pnr id，存储到mnjx_pnr_sub表
                // 检查是否已经关注过该PNR
                MnjxPnrSub existingSub = iMnjxPnrSubService.lambdaQuery()
                        .eq(MnjxPnrSub::getSiId, siId)
                        .eq(MnjxPnrSub::getPnrId, pnr.getPnrId())
                        .one();

                if (existingSub == null) {
                    // 如果没有关注过，则新增关注记录
                    MnjxPnrSub pnrSub = new MnjxPnrSub();
                    pnrSub.setSiId(siId);
                    pnrSub.setPnrId(pnr.getPnrId());
                    iMnjxPnrSubService.save(pnrSub);
                }
            }

            SubQueueCrsPnrResVo vo = new SubQueueCrsPnrResVo();
            vo.setSuccess(true);
            vo.setPnrDetailDtos(null);
            return vo;

        } catch (SguiResultException e) {
            throw e;
        } catch (Exception e) {
            log.error("关注PNR失败", e);
            throw new SguiResultException("关注PNR失败: " + e.getMessage());
        }
    }

    @Override
    public Boolean unsubscribeCrsQueue(UnsubscribeCrsQueueDto dto) throws SguiResultException {
        try {
            // 获取当前登录用户的工作号ID
            String siId = iSguiCommonService.getCurrentUserInfo().getSiId();

            // 1. 根据请求PNR编号列表查询mnjx_pnr表的pnr_crs字段值，如果不存在，报错：PNR不存在
            for (String pnrNo : dto.getPnrNos()) {
                MnjxPnr pnr = iMnjxPnrService.lambdaQuery()
                        .eq(MnjxPnr::getPnrCrs, pnrNo)
                        .one();

                if (pnr == null) {
                    throw new SguiResultException("PNR不存在: " + pnrNo);
                }

                // 2. 通过pnrId查询mnjx_pnr_sub表的pnrId字段，删除数据
                boolean removed = iMnjxPnrSubService.lambdaUpdate()
                        .eq(MnjxPnrSub::getSiId, siId)
                        .eq(MnjxPnrSub::getPnrId, pnr.getPnrId())
                        .remove();

                if (!removed) {
                    log.warn("未找到需要取消关注的PNR记录: {}, siId: {}", pnrNo, siId);
                }
            }

            return true;

        } catch (SguiResultException e) {
            throw e;
        } catch (Exception e) {
            log.error("取消关注PNR失败", e);
            throw new SguiResultException("取消关注PNR失败: " + e.getMessage());
        }
    }

    @Override
    public SubQueueCrsPnrDetailVo subQueueCrsPnrDetail(SubQueueCrsPnrDetailDto dto) throws SguiResultException {
        try {
            // 获取当前登录用户的工作号ID
            String siId = iSguiCommonService.getCurrentUserInfo().getSiId();

            SubQueueCrsPnrDetailVo vo = new SubQueueCrsPnrDetailVo();
            vo.setSuccess(true);
            List<SubQueueCrsPnrDetailVo.PnrDetailDto> pnrDetailDtos = new ArrayList<>();

            // 1. 根据请求PNR编号列表查询mnjx_pnr表的pnr_crs字段值，如果不存在，报错：PNR不存在
            for (String pnrNo : dto.getPnrNos()) {
                MnjxPnr pnr = iMnjxPnrService.lambdaQuery()
                        .eq(MnjxPnr::getPnrCrs, pnrNo)
                        .one();

                if (pnr == null) {
                    throw new SguiResultException("PNR不存在: " + pnrNo);
                }

                // 2. 获取当前登录的工作号id和查询的pnr id，存储到mnjx_pnr_sub表
                MnjxPnrSub existingSub = iMnjxPnrSubService.lambdaQuery()
                        .eq(MnjxPnrSub::getSiId, siId)
                        .eq(MnjxPnrSub::getPnrId, pnr.getPnrId())
                        .one();

                if (existingSub == null) {
                    // 如果没有关注过，则新增关注记录
                    MnjxPnrSub pnrSub = new MnjxPnrSub();
                    pnrSub.setSiId(siId);
                    pnrSub.setPnrId(pnr.getPnrId());
                    iMnjxPnrSubService.save(pnrSub);
                }

                // 3. 查询mnjx_pnr_nm和mnjx_pnr_seg表，构建返回数据
                SubQueueCrsPnrDetailVo.PnrDetailDto pnrDetailDto = this.buildPnrDetailDto(pnr);
                pnrDetailDtos.add(pnrDetailDto);
            }

            vo.setPnrDetailDtos(pnrDetailDtos);
            return vo;

        } catch (SguiResultException e) {
            throw e;
        } catch (Exception e) {
            log.error("关注PNR详情失败", e);
            throw new SguiResultException("关注PNR详情失败: " + e.getMessage());
        }
    }

    /**
     * 构建PNR详情DTO
     */
    private SubQueueCrsPnrDetailVo.PnrDetailDto buildPnrDetailDto(MnjxPnr pnr) {
        SubQueueCrsPnrDetailVo.PnrDetailDto pnrDetailDto = new SubQueueCrsPnrDetailVo.PnrDetailDto();
        pnrDetailDto.setPnrNo(pnr.getPnrCrs());
        pnrDetailDto.setGroup(false); // 根据需求，暂时设置为false

        // 4. 如果pnrStatus为DEL，canceled设置为true，issueStatus设置为0
        if ("DEL".equals(pnr.getPnrStatus())) {
            pnrDetailDto.setCanceled(true);
            pnrDetailDto.setIssueStatus("0");
        } else {
            pnrDetailDto.setCanceled(false);
            // 5. 如果不是DEL，根据复杂的业务规则设置issueStatus
            String issueStatus = this.calculateIssueStatus(pnr);
            pnrDetailDto.setIssueStatus(issueStatus);
        }

        // 查询旅客姓名列表
        List<String> passengerNames = this.getPassengerNames(pnr.getPnrId());
        pnrDetailDto.setPassengerNames(passengerNames);

        // 查询航班信息列表
        List<SubQueueCrsPnrDetailVo.FlightInfo> flights = this.getFlightInfos(pnr.getPnrId());
        pnrDetailDto.setFlights(flights);

        return pnrDetailDto;
    }

    /**
     * 计算出票状态
     * 5. 如果不是DEL，根据pnrNm查询nmXn，通过pnrNmId和xnNmId查询mnjx_pnr_nm_tn表，
     * 如果没有记录，issueStatus设置为0。
     * 如果有记录且mnjx_pnr_seg中没有exchanged为1的记录，issueStatus设置为2，否则设置为0。
     * 如果有记录且pnrNmList数量+nmXnList数量不等于nmTn数量，issueStatus设置为1
     */
    private String calculateIssueStatus(MnjxPnr pnr) {
        // 查询PNR下的旅客信息
        List<MnjxPnrNm> pnrNmList = iMnjxPnrNmService.lambdaQuery()
                .eq(MnjxPnrNm::getPnrId, pnr.getPnrId())
                .list();

        if (pnrNmList.isEmpty()) {
            return "0";
        }

        // 查询婴儿信息
        List<String> pnrNmIds = pnrNmList.stream()
                .map(MnjxPnrNm::getPnrNmId)
                .collect(Collectors.toList());

        List<MnjxNmXn> nmXnList = iMnjxNmXnService.lambdaQuery()
                .in(MnjxNmXn::getPnrNmId, pnrNmIds)
                .list();

        // 查询出票记录
        List<String> allNmIds = new ArrayList<>(pnrNmIds);
        if (!nmXnList.isEmpty()) {
            List<String> xnNmIds = nmXnList.stream()
                    .map(MnjxNmXn::getNmXnId)
                    .collect(Collectors.toList());
            allNmIds.addAll(xnNmIds);
        }

        List<MnjxPnrNmTn> nmTnList = iMnjxPnrNmTnService.lambdaQuery()
                .in(MnjxPnrNmTn::getPnrNmId, allNmIds)
                .or()
                .in(MnjxPnrNmTn::getNmXnId, allNmIds)
                .list();

        // 如果没有出票记录，issueStatus设置为0
        if (nmTnList.isEmpty()) {
            return "0";
        }

        // 检查是否有改签的航段
        List<MnjxPnrSeg> exchangedSegs = iMnjxPnrSegService.lambdaQuery()
                .eq(MnjxPnrSeg::getPnrId, pnr.getPnrId())
                .eq(MnjxPnrSeg::getExchanged, "1")
                .list();

        // 如果有记录且mnjx_pnr_seg中没有exchanged为1的记录，issueStatus设置为2，否则设置为0
        if (!exchangedSegs.isEmpty()) {
            return "0";
        }

        // 如果有记录且pnrNmList数量+nmXnList数量不等于nmTn数量，issueStatus设置为1
        int totalPassengers = pnrNmList.size() + nmXnList.size();
        if (totalPassengers != nmTnList.size()) {
            return "1";
        }

        // 其他情况设置为2
        return "2";
    }

    /**
     * 获取旅客姓名列表
     */
    private List<String> getPassengerNames(String pnrId) {
        List<MnjxPnrNm> pnrNmList = iMnjxPnrNmService.lambdaQuery()
                .eq(MnjxPnrNm::getPnrId, pnrId)
                .list();

        return pnrNmList.stream()
                .map(MnjxPnrNm::getName)
                .collect(Collectors.toList());
    }

    /**
     * 获取航班信息列表
     */
    private List<SubQueueCrsPnrDetailVo.FlightInfo> getFlightInfos(String pnrId) {
        List<MnjxPnrSeg> pnrSegList = iMnjxPnrSegService.lambdaQuery()
                .eq(MnjxPnrSeg::getPnrId, pnrId)
                .orderByAsc(MnjxPnrSeg::getPnrSegNo)
                .list();

        List<SubQueueCrsPnrDetailVo.FlightInfo> flights = new ArrayList<>();
        for (MnjxPnrSeg seg : pnrSegList) {
            SubQueueCrsPnrDetailVo.FlightInfo flightInfo = new SubQueueCrsPnrDetailVo.FlightInfo();
            flightInfo.setFlightNo(seg.getFlightNo());
            flightInfo.setDepartureCode(seg.getOrg());
            flightInfo.setArrivalCode(seg.getDst());

            // 设置出发日期和时间
            if (seg.getFlightDate() != null) {
                String departureDate = seg.getFlightDate();
                flightInfo.setDepartureDate(departureDate);

                // 构建出发日期时间（假设起飞时间在depTime字段中）
                if (seg.getEstimateOff() != null) {
                    String departureDateTime = departureDate + " " + seg.getEstimateOff().substring(0, 2) + ":" + seg.getEstimateOff().substring(2);
                    flightInfo.setDepartureDateTime(departureDateTime);
                } else {
                    flightInfo.setDepartureDateTime(departureDate + " 00:00");
                }
            }

            flightInfo.setCabinName(seg.getSellCabin());
            flightInfo.setActionCode(seg.getActionCode());

            flights.add(flightInfo);
        }

        return flights;
    }
}
