package com.swcares.service.bkc;

import com.swcares.core.exception.SguiResultException;
import com.swcares.obj.dto.*;
import com.swcares.obj.vo.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/23 14:34
 */
public interface IBkcV2Service {

    CheckVersionVo checkVersion(CheckVersionDto dto);

    List<String> domesticairline();

    /**
     * 查询PNR详情
     *
     * @param dto 查询参数
     * @return PNR详情
     * @throws SguiResultException 异常
     */
    QueryPnrDetailVo queryPnrDetail(QueryPnrDetailDto dto) throws SguiResultException;

    /**
     * 查询PNR历史
     *
     * @param pnrNo PNR编号
     * @return PNR历史信息
     * @throws SguiResultException 异常
     */
    PnrHistoryVo queryPnrHistory(String pnrNo) throws SguiResultException;

    /**
     * 查询航司PNR
     *
     * @param dto 查询参数
     * @return 航司PNR信息
     * @throws SguiResultException 异常
     */
    QueryPnrRtlVo queryPnrRtl(QueryPnrRtlDto dto) throws SguiResultException;

    /**
     * 强制刷新PNR
     * 将婴儿SSR INFT的行动代码修改为HK
     *
     * @param dto 请求参数
     * @throws SguiResultException 异常
     */
    void strongRefresh(StrongRefreshDto dto) throws SguiResultException;

    /**
     * 更新PNR（封口）
     *
     * @param reqDto 请求参数（包含base64编码的数据）
     * @return 更新结果
     * @throws SguiResultException 异常
     */
    UpdatePnrVo updatePnr(UpdatePnrReqDto reqDto) throws SguiResultException;

    /**
     * 查询预选座位
     *
     * @param dto 查询参数
     * @return 座位图信息
     * @throws SguiResultException 异常
     */
    Object querySeatMap(QuerySeatMapDto dto) throws SguiResultException;

    /**
     * 出票
     *
     * @param dto 出票请求参数
     * @return 出票结果
     * @throws SguiResultException 异常
     */
    IssueTicketVo issueTicket(IssueTicketDto dto) throws SguiResultException;

    /**
     * 删除旅客
     *
     * @param dto 删除旅客请求参数
     * @throws SguiResultException 异常
     */
    void removeName(RemoveNameDto dto) throws SguiResultException;

    /**
     * 分离旅客
     *
     * @param dto 分离旅客请求参数
     * @return 分离结果
     * @throws SguiResultException 异常
     */
    SplitPnrByPassengerVo splitPnrByPassenger(SplitPnrByPassengerDto dto) throws SguiResultException;

    /**
     * 取消PNR
     *
     * @param dto 取消PNR请求参数
     * @return PNR编号
     * @throws SguiResultException 异常
     */
    String xePnr(XePnrDto dto) throws SguiResultException;

    /**
     * 预览票面
     *
     * @param dto 预览票面请求参数
     * @return 预览票面结果
     * @throws SguiResultException 异常
     */
    List<StructuredPreviewVo> structuredPreview(StructuredPreviewDto dto) throws SguiResultException;

    /**
     * 通过姓名查询PNR
     *
     * @param dto 查询参数
     * @return PNR查询结果
     * @throws SguiResultException 异常
     */
    QueryPnrByNameVo queryPnrByName(QueryPnrByNameDto dto) throws SguiResultException;

    /**
     * 查询关注PNR列表
     *
     * @param dto 查询参数
     * @return 关注PNR列表
     * @throws SguiResultException 异常
     */
    SubQueueCrsPnrVo queryQueueList(SubQueueCrsPnrDto dto) throws SguiResultException;

    /**
     * 关注PNR
     *
     * @param dto 关注PNR请求参数
     * @return 关注结果
     * @throws SguiResultException 异常
     */
    SubQueueCrsPnrResVo subQueueCrsPnr(SubQueueCrsPnrReqDto dto) throws SguiResultException;

    /**
     * 取消关注PNR
     *
     * @param dto 取消关注PNR请求参数
     * @return 取消关注结果
     * @throws SguiResultException 异常
     */
    Boolean unsubscribeCrsQueue(UnsubscribeCrsQueueDto dto) throws SguiResultException;
}
