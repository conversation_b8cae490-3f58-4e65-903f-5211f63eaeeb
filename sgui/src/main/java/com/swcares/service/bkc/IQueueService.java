package com.swcares.service.bkc;

import com.swcares.core.exception.SguiResultException;
import com.swcares.obj.dto.SubQueueCrsPnrDto;
import com.swcares.obj.vo.SubQueueCrsPnrVo;

/**
 * 队列服务接口
 *
 * <AUTHOR>
 * @date 2025/08/04 17:30
 */
public interface IQueueService {

    /**
     * 查询关注PNR列表
     *
     * @param dto 查询参数
     * @return 关注PNR列表
     * @throws SguiResultException 异常
     */
    SubQueueCrsPnrVo queryQueueList(SubQueueCrsPnrDto dto) throws SguiResultException;
}
