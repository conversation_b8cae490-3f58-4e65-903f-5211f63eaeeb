<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.MnjxPnrSubMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.entity.MnjxPnrSub">
        <id column="mnjx_pnr_sub_id" property="mnjxPnrSubId" />
        <result column="si_id" property="siId" />
        <result column="pnr_id" property="pnrId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        mnjx_pnr_sub_id, si_id, pnr_id
    </sql>

</mapper>
