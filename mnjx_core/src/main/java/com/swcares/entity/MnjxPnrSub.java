package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_pnr_sub")
@ApiModel(value="MnjxPnrSub对象", description="")
public class MnjxPnrSub extends Model<MnjxPnrSub> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "关注ID")
    @TableId(value = "mnjx_pnr_sub_id", type = IdType.ASSIGN_ID)
    private String mnjxPnrSubId;

    @ApiModelProperty(value = "工作号ID")
    @TableField("si_id")
    private String siId;

    @ApiModelProperty(value = "关注的PNR ID")
    @TableField("pnr_id")
    private String pnrId;


    @Override
    protected Serializable pkVal() {
        return this.mnjxPnrSubId;
    }

}
